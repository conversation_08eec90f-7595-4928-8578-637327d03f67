{"version": 3, "targets": {"net8.0": {"Azure.Core/1.35.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.10.3": {"type": "package", "dependencies": {"Azure.Core": "1.35.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Humanizer/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core.af": "2.14.1", "Humanizer.Core.ar": "2.14.1", "Humanizer.Core.az": "2.14.1", "Humanizer.Core.bg": "2.14.1", "Humanizer.Core.bn-BD": "2.14.1", "Humanizer.Core.cs": "2.14.1", "Humanizer.Core.da": "2.14.1", "Humanizer.Core.de": "2.14.1", "Humanizer.Core.el": "2.14.1", "Humanizer.Core.es": "2.14.1", "Humanizer.Core.fa": "2.14.1", "Humanizer.Core.fi-FI": "2.14.1", "Humanizer.Core.fr": "2.14.1", "Humanizer.Core.fr-BE": "2.14.1", "Humanizer.Core.he": "2.14.1", "Humanizer.Core.hr": "2.14.1", "Humanizer.Core.hu": "2.14.1", "Humanizer.Core.hy": "2.14.1", "Humanizer.Core.id": "2.14.1", "Humanizer.Core.is": "2.14.1", "Humanizer.Core.it": "2.14.1", "Humanizer.Core.ja": "2.14.1", "Humanizer.Core.ko-KR": "2.14.1", "Humanizer.Core.ku": "2.14.1", "Humanizer.Core.lv": "2.14.1", "Humanizer.Core.ms-MY": "2.14.1", "Humanizer.Core.mt": "2.14.1", "Humanizer.Core.nb": "2.14.1", "Humanizer.Core.nb-NO": "2.14.1", "Humanizer.Core.nl": "2.14.1", "Humanizer.Core.pl": "2.14.1", "Humanizer.Core.pt": "2.14.1", "Humanizer.Core.ro": "2.14.1", "Humanizer.Core.ru": "2.14.1", "Humanizer.Core.sk": "2.14.1", "Humanizer.Core.sl": "2.14.1", "Humanizer.Core.sr": "2.14.1", "Humanizer.Core.sr-Latn": "2.14.1", "Humanizer.Core.sv": "2.14.1", "Humanizer.Core.th-TH": "2.14.1", "Humanizer.Core.tr": "2.14.1", "Humanizer.Core.uk": "2.14.1", "Humanizer.Core.uz-Cyrl-UZ": "2.14.1", "Humanizer.Core.uz-Latn-UZ": "2.14.1", "Humanizer.Core.vi": "2.14.1", "Humanizer.Core.zh-CN": "2.14.1", "Humanizer.Core.zh-Hans": "2.14.1", "Humanizer.Core.zh-Hant": "2.14.1"}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "Humanizer.Core.af/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/af/Humanizer.resources.dll": {"locale": "af"}}}, "Humanizer.Core.ar/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/ar/Humanizer.resources.dll": {"locale": "ar"}}}, "Humanizer.Core.az/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/az/Humanizer.resources.dll": {"locale": "az"}}}, "Humanizer.Core.bg/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/bg/Humanizer.resources.dll": {"locale": "bg"}}}, "Humanizer.Core.bn-BD/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/bn-BD/Humanizer.resources.dll": {"locale": "bn-BD"}}}, "Humanizer.Core.cs/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/cs/Humanizer.resources.dll": {"locale": "cs"}}}, "Humanizer.Core.da/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/da/Humanizer.resources.dll": {"locale": "da"}}}, "Humanizer.Core.de/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/de/Humanizer.resources.dll": {"locale": "de"}}}, "Humanizer.Core.el/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/el/Humanizer.resources.dll": {"locale": "el"}}}, "Humanizer.Core.es/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/es/Humanizer.resources.dll": {"locale": "es"}}}, "Humanizer.Core.fa/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/fa/Humanizer.resources.dll": {"locale": "fa"}}}, "Humanizer.Core.fi-FI/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/fi-FI/Humanizer.resources.dll": {"locale": "fi-FI"}}}, "Humanizer.Core.fr/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/fr/Humanizer.resources.dll": {"locale": "fr"}}}, "Humanizer.Core.fr-BE/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/fr-BE/Humanizer.resources.dll": {"locale": "fr-BE"}}}, "Humanizer.Core.he/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/he/Humanizer.resources.dll": {"locale": "he"}}}, "Humanizer.Core.hr/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/hr/Humanizer.resources.dll": {"locale": "hr"}}}, "Humanizer.Core.hu/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/hu/Humanizer.resources.dll": {"locale": "hu"}}}, "Humanizer.Core.hy/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/hy/Humanizer.resources.dll": {"locale": "hy"}}}, "Humanizer.Core.id/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/id/Humanizer.resources.dll": {"locale": "id"}}}, "Humanizer.Core.is/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/is/Humanizer.resources.dll": {"locale": "is"}}}, "Humanizer.Core.it/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/it/Humanizer.resources.dll": {"locale": "it"}}}, "Humanizer.Core.ja/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/ja/Humanizer.resources.dll": {"locale": "ja"}}}, "Humanizer.Core.ko-KR/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/netstandard2.0/ko-KR/Humanizer.resources.dll": {"locale": "ko-KR"}}}, "Humanizer.Core.ku/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/ku/Humanizer.resources.dll": {"locale": "ku"}}}, "Humanizer.Core.lv/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/lv/Humanizer.resources.dll": {"locale": "lv"}}}, "Humanizer.Core.ms-MY/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/netstandard2.0/ms-MY/Humanizer.resources.dll": {"locale": "ms-MY"}}}, "Humanizer.Core.mt/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/netstandard2.0/mt/Humanizer.resources.dll": {"locale": "mt"}}}, "Humanizer.Core.nb/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/nb/Humanizer.resources.dll": {"locale": "nb"}}}, "Humanizer.Core.nb-NO/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/nb-NO/Humanizer.resources.dll": {"locale": "nb-NO"}}}, "Humanizer.Core.nl/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/nl/Humanizer.resources.dll": {"locale": "nl"}}}, "Humanizer.Core.pl/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/pl/Humanizer.resources.dll": {"locale": "pl"}}}, "Humanizer.Core.pt/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/pt/Humanizer.resources.dll": {"locale": "pt"}}}, "Humanizer.Core.ro/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/ro/Humanizer.resources.dll": {"locale": "ro"}}}, "Humanizer.Core.ru/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/ru/Humanizer.resources.dll": {"locale": "ru"}}}, "Humanizer.Core.sk/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/sk/Humanizer.resources.dll": {"locale": "sk"}}}, "Humanizer.Core.sl/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/sl/Humanizer.resources.dll": {"locale": "sl"}}}, "Humanizer.Core.sr/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/sr/Humanizer.resources.dll": {"locale": "sr"}}}, "Humanizer.Core.sr-Latn/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/sr-Latn/Humanizer.resources.dll": {"locale": "sr-Latn"}}}, "Humanizer.Core.sv/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/sv/Humanizer.resources.dll": {"locale": "sv"}}}, "Humanizer.Core.th-TH/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/netstandard2.0/th-TH/Humanizer.resources.dll": {"locale": "th-TH"}}}, "Humanizer.Core.tr/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/tr/Humanizer.resources.dll": {"locale": "tr"}}}, "Humanizer.Core.uk/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/uk/Humanizer.resources.dll": {"locale": "uk"}}}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/uz-Cyrl-UZ/Humanizer.resources.dll": {"locale": "uz-Cyrl-UZ"}}}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/uz-Latn-UZ/Humanizer.resources.dll": {"locale": "uz-Latn-UZ"}}}, "Humanizer.Core.vi/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/vi/Humanizer.resources.dll": {"locale": "vi"}}}, "Humanizer.Core.zh-CN/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/zh-CN/Humanizer.resources.dll": {"locale": "zh-CN"}}}, "Humanizer.Core.zh-Hans/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/zh-Hans/Humanizer.resources.dll": {"locale": "zh-Hans"}}}, "Humanizer.Core.zh-Hant/2.14.1": {"type": "package", "dependencies": {"Humanizer.Core": "[2.14.1]"}, "resource": {"lib/net6.0/zh-Hant/Humanizer.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.AspNetCore.Razor.Language/6.0.24": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Build/17.8.3": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.NET.StringTools": "17.8.3", "System.Collections.Immutable": "7.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Reflection.MetadataLoadContext": "7.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading.Tasks.Dataflow": "7.0.0"}, "compile": {"ref/net8.0/Microsoft.Build.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Build.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "compile": {"ref/net8.0/Microsoft.Build.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Build.Framework.dll": {"related": ".pdb;.xml"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "build": {"buildTransitive/Microsoft.CodeAnalysis.Analyzers.props": {}, "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets": {}}}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll": {"related": ".xml"}}}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.8.0]"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Features/4.8.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.8.0]", "Microsoft.CodeAnalysis.CSharp.Workspaces": "[4.8.0]", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Features": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.8.0]", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "4.5.0", "System.Data.DataSetExtensions": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Elfie.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Elfie.dll": {}}}, "Microsoft.CodeAnalysis.Features/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.AnalyzerUtilities": "3.3.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Scripting.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]", "Microsoft.DiaSymReader": "2.0.0", "System.Text.Json": "7.0.3"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Features.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Features.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/6.0.24": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CodeAnalysis.Scripting.Common/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.8.0]"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Scripting.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Scripting.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/5.1.5": {"type": "package", "dependencies": {"Azure.Identity": "1.10.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.56.0", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "compile": {"ref/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.DiaSymReader/2.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.DiaSymReader.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.DiaSymReader.dll": {"related": ".pdb;.xml"}}}, "Microsoft.DotNet.Scaffolding.Shared/8.0.7": {"type": "package", "dependencies": {"Humanizer": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.Extensions.DependencyModel": "8.0.2", "Mono.TextTemplating": "2.3.1", "Newtonsoft.Json": "13.0.3", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "8.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.DotNet.Scaffolding.Shared.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.DotNet.Scaffolding.Shared.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/8.0.11": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.11", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.11", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.11": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.11": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Design/8.0.11": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.11", "Microsoft.Extensions.DependencyModel": "8.0.2", "Mono.TextTemplating": "2.2.1"}, "compile": {"lib/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.11": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.11", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.5": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "5.1.5", "Microsoft.EntityFrameworkCore.Relational": "8.0.5"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.11": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.11"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.22.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NET.StringTools/17.8.3": {"type": "package", "compile": {"ref/net8.0/Microsoft.NET.StringTools.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"related": ".pdb;.xml"}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.VisualStudio.Web.CodeGeneration/8.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "8.0.7"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {"related": ".xml"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/8.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.VisualStudio.Web.CodeGeneration.Templating": "8.0.7", "Newtonsoft.Json": "13.0.3"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {"related": ".xml"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/8.0.7": {"type": "package", "dependencies": {"Microsoft.DotNet.Scaffolding.Shared": "8.0.7", "Microsoft.VisualStudio.Web.CodeGenerators.Mvc": "8.0.7"}, "compile": {"lib/net8.0/dotnet-aspnet-codegenerator-design.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/dotnet-aspnet-codegenerator-design.dll": {"related": ".xml"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/8.0.7": {"type": "package", "dependencies": {"Microsoft.DotNet.Scaffolding.Shared": "8.0.7", "Microsoft.VisualStudio.Web.CodeGeneration.Core": "8.0.7"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {"related": ".runtimeconfig.json;.xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {"related": ".runtimeconfig.json;.xml"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/8.0.7": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.VisualStudio.Web.CodeGeneration.Utils": "8.0.7"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {"related": ".xml"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/8.0.7": {"type": "package", "dependencies": {"Microsoft.Build": "17.8.3", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "8.0.7", "Newtonsoft.Json": "13.0.3"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {"related": ".xml"}}}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/8.0.7": {"type": "package", "dependencies": {"Microsoft.DotNet.Scaffolding.Shared": "8.0.7", "Microsoft.VisualStudio.Web.CodeGeneration": "8.0.7"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {"related": ".xml"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Mono.TextTemplating/2.3.1": {"type": "package", "dependencies": {"System.CodeDom": "5.0.0"}, "compile": {"lib/netcoreapp3.1/Mono.TextTemplating.dll": {}}, "runtime": {"lib/netcoreapp3.1/Mono.TextTemplating.dll": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NuGet.Common/6.11.0": {"type": "package", "dependencies": {"NuGet.Frameworks": "6.11.0"}, "compile": {"lib/netstandard2.0/NuGet.Common.dll": {}}, "runtime": {"lib/netstandard2.0/NuGet.Common.dll": {}}}, "NuGet.Configuration/6.11.0": {"type": "package", "dependencies": {"NuGet.Common": "6.11.0", "System.Security.Cryptography.ProtectedData": "4.4.0"}, "compile": {"lib/netstandard2.0/NuGet.Configuration.dll": {}}, "runtime": {"lib/netstandard2.0/NuGet.Configuration.dll": {}}}, "NuGet.DependencyResolver.Core/6.11.0": {"type": "package", "dependencies": {"NuGet.Configuration": "6.11.0", "NuGet.LibraryModel": "6.11.0", "NuGet.Protocol": "6.11.0"}, "compile": {"lib/net5.0/NuGet.DependencyResolver.Core.dll": {}}, "runtime": {"lib/net5.0/NuGet.DependencyResolver.Core.dll": {}}}, "NuGet.Frameworks/6.11.0": {"type": "package", "compile": {"lib/netstandard2.0/NuGet.Frameworks.dll": {}}, "runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {}}}, "NuGet.LibraryModel/6.11.0": {"type": "package", "dependencies": {"NuGet.Common": "6.11.0", "NuGet.Versioning": "6.11.0"}, "compile": {"lib/netstandard2.0/NuGet.LibraryModel.dll": {}}, "runtime": {"lib/netstandard2.0/NuGet.LibraryModel.dll": {}}}, "NuGet.Packaging/6.11.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.3", "NuGet.Configuration": "6.11.0", "NuGet.Versioning": "6.11.0", "System.Security.Cryptography.Pkcs": "6.0.4"}, "compile": {"lib/net5.0/NuGet.Packaging.dll": {}}, "runtime": {"lib/net5.0/NuGet.Packaging.dll": {}}}, "NuGet.ProjectModel/6.11.0": {"type": "package", "dependencies": {"NuGet.DependencyResolver.Core": "6.11.0"}, "compile": {"lib/net5.0/NuGet.ProjectModel.dll": {}}, "runtime": {"lib/net5.0/NuGet.ProjectModel.dll": {}}}, "NuGet.Protocol/6.11.0": {"type": "package", "dependencies": {"NuGet.Packaging": "6.11.0"}, "compile": {"lib/net5.0/NuGet.Protocol.dll": {}}, "runtime": {"lib/net5.0/NuGet.Protocol.dll": {}}}, "NuGet.Versioning/6.11.0": {"type": "package", "compile": {"lib/netstandard2.0/NuGet.Versioning.dll": {}}, "runtime": {"lib/netstandard2.0/NuGet.Versioning.dll": {}}}, "System.CodeDom/5.0.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}}, "System.Collections.Immutable/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}, "compile": {"lib/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Convention/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "compile": {"lib/net7.0/System.Composition.Convention.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Hosting/7.0.0": {"type": "package", "dependencies": {"System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Runtime/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.TypedParts/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "compile": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Data.DataSetExtensions.dll": {}}, "runtime": {"lib/netstandard2.0/System.Data.DataSetExtensions.dll": {}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/7.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "compile": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "compile": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Pipelines/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Metadata/7.0.0": {"type": "package", "dependencies": {"System.Collections.Immutable": "7.0.0"}, "compile": {"lib/net7.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Reflection.MetadataLoadContext/7.0.0": {"type": "package", "dependencies": {"System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0"}, "compile": {"lib/net7.0/System.Reflection.MetadataLoadContext.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Reflection.MetadataLoadContext.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/6.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "dependencies": {"System.Formats.Asn1": "5.0.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/6.0.4": {"type": "package", "dependencies": {"System.Formats.Asn1": "6.0.0"}, "compile": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/7.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading.Channels/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Threading.Tasks.Dataflow/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Windows.Extensions/7.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "7.0.0"}, "compile": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "DrugPrevention.Repositories.NamND/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.5", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.5", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0"}, "compile": {"bin/placeholder/DrugPrevention.Repositories.NamND.dll": {}}, "runtime": {"bin/placeholder/DrugPrevention.Repositories.NamND.dll": {}}}, "DrugPrevention.Services.NamND/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"DrugPrevention.Repositories.NamND": "1.0.0"}, "compile": {"bin/placeholder/DrugPrevention.Services.NamND.dll": {}}, "runtime": {"bin/placeholder/DrugPrevention.Services.NamND.dll": {}}}}}, "libraries": {"Azure.Core/1.35.0": {"sha512": "hENcx03Jyuqv05F4RBEPbxz29UrM3Nbhnr6Wl6NQpoU9BCIbL3XLentrxDCTrH54NLS11Exxi/o8MYgT/cnKFA==", "type": "package", "path": "azure.core/1.35.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.35.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net5.0/Azure.Core.dll", "lib/net5.0/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netcoreapp2.1/Azure.Core.dll", "lib/netcoreapp2.1/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.10.3": {"sha512": "l1Xm2MWOF2Mzcwuarlw8kWQXLZk3UeB55aQXVyjj23aBfDwOZ3gu5GP2kJ6KlmZeZv2TCzw7x4L3V36iNr3gww==", "type": "package", "path": "azure.identity/1.10.3", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.10.3.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Humanizer/2.14.1": {"sha512": "/FUTD3cEceAAmJSCPN9+J+VhGwmL/C12jvwlyM1DFXShEMsBzvLzLqSrJ2rb+k/W2znKw7JyflZgZpyE+tI7lA==", "type": "package", "path": "humanizer/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.2.14.1.nupkg.sha512", "humanizer.nuspec", "logo.png"]}, "Humanizer.Core/2.14.1": {"sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "type": "package", "path": "humanizer.core/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.14.1.nupkg.sha512", "humanizer.core.nuspec", "lib/net6.0/Humanizer.dll", "lib/net6.0/Humanizer.xml", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "Humanizer.Core.af/2.14.1": {"sha512": "BoQHyu5le+xxKOw+/AUM7CLXneM/Bh3++0qh1u0+D95n6f9eGt9kNc8LcAHLIOwId7Sd5hiAaaav0Nimj3peNw==", "type": "package", "path": "humanizer.core.af/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.af.2.14.1.nupkg.sha512", "humanizer.core.af.nuspec", "lib/net6.0/af/Humanizer.resources.dll", "lib/netstandard1.0/af/Humanizer.resources.dll", "lib/netstandard2.0/af/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.ar/2.14.1": {"sha512": "3d1V10LDtmqg5bZjWkA/EkmGFeSfNBcyCH+TiHcHP+HGQQmRq3eBaLcLnOJbVQVn3Z6Ak8GOte4RX4kVCxQlFA==", "type": "package", "path": "humanizer.core.ar/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.ar.2.14.1.nupkg.sha512", "humanizer.core.ar.nuspec", "lib/net6.0/ar/Humanizer.resources.dll", "lib/netstandard1.0/ar/Humanizer.resources.dll", "lib/netstandard2.0/ar/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.az/2.14.1": {"sha512": "8Z/tp9PdHr/K2Stve2Qs/7uqWPWLUK9D8sOZDNzyv42e20bSoJkHFn7SFoxhmaoVLJwku2jp6P7HuwrfkrP18Q==", "type": "package", "path": "humanizer.core.az/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.az.2.14.1.nupkg.sha512", "humanizer.core.az.nuspec", "lib/net6.0/az/Humanizer.resources.dll", "lib/netstandard1.0/az/Humanizer.resources.dll", "lib/netstandard2.0/az/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.bg/2.14.1": {"sha512": "S+hIEHicrOcbV2TBtyoPp1AVIGsBzlarOGThhQYCnP6QzEYo/5imtok6LMmhZeTnBFoKhM8yJqRfvJ5yqVQKSQ==", "type": "package", "path": "humanizer.core.bg/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.bg.2.14.1.nupkg.sha512", "humanizer.core.bg.nuspec", "lib/net6.0/bg/Humanizer.resources.dll", "lib/netstandard1.0/bg/Humanizer.resources.dll", "lib/netstandard2.0/bg/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.bn-BD/2.14.1": {"sha512": "U3bfj90tnUDRKlL1ZFlzhCHoVgpTcqUlTQxjvGCaFKb+734TTu3nkHUWVZltA1E/swTvimo/aXLtkxnLFrc0EQ==", "type": "package", "path": "humanizer.core.bn-bd/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.bn-bd.2.14.1.nupkg.sha512", "humanizer.core.bn-bd.nuspec", "lib/net6.0/bn-BD/Humanizer.resources.dll", "lib/netstandard1.0/bn-BD/Humanizer.resources.dll", "lib/netstandard2.0/bn-BD/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.cs/2.14.1": {"sha512": "jWrQkiCTy3L2u1T86cFkgijX6k7hoB0pdcFMWYaSZnm6rvG/XJE40tfhYyKhYYgIc1x9P2GO5AC7xXvFnFdqMQ==", "type": "package", "path": "humanizer.core.cs/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.cs.2.14.1.nupkg.sha512", "humanizer.core.cs.nuspec", "lib/net6.0/cs/Humanizer.resources.dll", "lib/netstandard1.0/cs/Humanizer.resources.dll", "lib/netstandard2.0/cs/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.da/2.14.1": {"sha512": "5o0rJyE/2wWUUphC79rgYDnif/21MKTTx9LIzRVz9cjCIVFrJ2bDyR2gapvI9D6fjoyvD1NAfkN18SHBsO8S9g==", "type": "package", "path": "humanizer.core.da/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.da.2.14.1.nupkg.sha512", "humanizer.core.da.nuspec", "lib/net6.0/da/Humanizer.resources.dll", "lib/netstandard1.0/da/Humanizer.resources.dll", "lib/netstandard2.0/da/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.de/2.14.1": {"sha512": "9JD/p+rqjb8f5RdZ3aEJqbjMYkbk4VFii2QDnnOdNo6ywEfg/A5YeOQ55CaBJmy7KvV4tOK4+qHJnX/tg3Z54A==", "type": "package", "path": "humanizer.core.de/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.de.2.14.1.nupkg.sha512", "humanizer.core.de.nuspec", "lib/net6.0/de/Humanizer.resources.dll", "lib/netstandard1.0/de/Humanizer.resources.dll", "lib/netstandard2.0/de/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.el/2.14.1": {"sha512": "Xmv6sTL5mqjOWGGpqY7bvbfK5RngaUHSa8fYDGSLyxY9mGdNbDcasnRnMOvi0SxJS9gAqBCn21Xi90n2SHZbFA==", "type": "package", "path": "humanizer.core.el/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.el.2.14.1.nupkg.sha512", "humanizer.core.el.nuspec", "lib/net6.0/el/Humanizer.resources.dll", "lib/netstandard1.0/el/Humanizer.resources.dll", "lib/netstandard2.0/el/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.es/2.14.1": {"sha512": "e//OIAeMB7pjBV1HqqI4pM2Bcw3Jwgpyz9G5Fi4c+RJvhqFwztoWxW57PzTnNJE2lbhGGLQZihFZjsbTUsbczA==", "type": "package", "path": "humanizer.core.es/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.es.2.14.1.nupkg.sha512", "humanizer.core.es.nuspec", "lib/net6.0/es/Humanizer.resources.dll", "lib/netstandard1.0/es/Humanizer.resources.dll", "lib/netstandard2.0/es/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.fa/2.14.1": {"sha512": "nzDOj1x0NgjXMjsQxrET21t1FbdoRYujzbmZoR8u8ou5CBWY1UNca0j6n/PEJR/iUbt4IxstpszRy41wL/BrpA==", "type": "package", "path": "humanizer.core.fa/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.fa.2.14.1.nupkg.sha512", "humanizer.core.fa.nuspec", "lib/net6.0/fa/Humanizer.resources.dll", "lib/netstandard1.0/fa/Humanizer.resources.dll", "lib/netstandard2.0/fa/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.fi-FI/2.14.1": {"sha512": "Vnxxx4LUhp3AzowYi6lZLAA9Lh8UqkdwRh4IE2qDXiVpbo08rSbokATaEzFS+o+/jCNZBmoyyyph3vgmcSzhhQ==", "type": "package", "path": "humanizer.core.fi-fi/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.fi-fi.2.14.1.nupkg.sha512", "humanizer.core.fi-fi.nuspec", "lib/net6.0/fi-FI/Humanizer.resources.dll", "lib/netstandard1.0/fi-FI/Humanizer.resources.dll", "lib/netstandard2.0/fi-FI/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.fr/2.14.1": {"sha512": "2p4g0BYNzFS3u9SOIDByp2VClYKO0K1ecDV4BkB9EYdEPWfFODYnF+8CH8LpUrpxL2TuWo2fiFx/4Jcmrnkbpg==", "type": "package", "path": "humanizer.core.fr/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.fr.2.14.1.nupkg.sha512", "humanizer.core.fr.nuspec", "lib/net6.0/fr/Humanizer.resources.dll", "lib/netstandard1.0/fr/Humanizer.resources.dll", "lib/netstandard2.0/fr/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.fr-BE/2.14.1": {"sha512": "o6R3SerxCRn5Ij8nCihDNMGXlaJ/1AqefteAssgmU2qXYlSAGdhxmnrQAXZUDlE4YWt/XQ6VkNLtH7oMqsSPFQ==", "type": "package", "path": "humanizer.core.fr-be/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.fr-be.2.14.1.nupkg.sha512", "humanizer.core.fr-be.nuspec", "lib/net6.0/fr-BE/Humanizer.resources.dll", "lib/netstandard1.0/fr-BE/Humanizer.resources.dll", "lib/netstandard2.0/fr-BE/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.he/2.14.1": {"sha512": "FPsAhy7Iw6hb+ZitLgYC26xNcgGAHXb0V823yFAzcyoL5ozM+DCJtYfDPYiOpsJhEZmKFTM9No0jUn1M89WGvg==", "type": "package", "path": "humanizer.core.he/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.he.2.14.1.nupkg.sha512", "humanizer.core.he.nuspec", "lib/net6.0/he/Humanizer.resources.dll", "lib/netstandard1.0/he/Humanizer.resources.dll", "lib/netstandard2.0/he/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.hr/2.14.1": {"sha512": "chnaD89yOlST142AMkAKLuzRcV5df3yyhDyRU5rypDiqrq2HN8y1UR3h1IicEAEtXLoOEQyjSAkAQ6QuXkn7aw==", "type": "package", "path": "humanizer.core.hr/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.hr.2.14.1.nupkg.sha512", "humanizer.core.hr.nuspec", "lib/net6.0/hr/Humanizer.resources.dll", "lib/netstandard1.0/hr/Humanizer.resources.dll", "lib/netstandard2.0/hr/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.hu/2.14.1": {"sha512": "hAfnaoF9LTGU/CmFdbnvugN4tIs8ppevVMe3e5bD24+tuKsggMc5hYta9aiydI8JH9JnuVmxvNI4DJee1tK05A==", "type": "package", "path": "humanizer.core.hu/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.hu.2.14.1.nupkg.sha512", "humanizer.core.hu.nuspec", "lib/net6.0/hu/Humanizer.resources.dll", "lib/netstandard1.0/hu/Humanizer.resources.dll", "lib/netstandard2.0/hu/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.hy/2.14.1": {"sha512": "sVIKxOiSBUb4gStRHo9XwwAg9w7TNvAXbjy176gyTtaTiZkcjr9aCPziUlYAF07oNz6SdwdC2mwJBGgvZ0Sl2g==", "type": "package", "path": "humanizer.core.hy/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.hy.2.14.1.nupkg.sha512", "humanizer.core.hy.nuspec", "lib/net6.0/hy/Humanizer.resources.dll", "lib/netstandard1.0/hy/Humanizer.resources.dll", "lib/netstandard2.0/hy/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.id/2.14.1": {"sha512": "4Zl3GTvk3a49Ia/WDNQ97eCupjjQRs2iCIZEQdmkiqyaLWttfb+cYXDMGthP42nufUL0SRsvBctN67oSpnXtsg==", "type": "package", "path": "humanizer.core.id/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.id.2.14.1.nupkg.sha512", "humanizer.core.id.nuspec", "lib/net6.0/id/Humanizer.resources.dll", "lib/netstandard1.0/id/Humanizer.resources.dll", "lib/netstandard2.0/id/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.is/2.14.1": {"sha512": "R67A9j/nNgcWzU7gZy1AJ07ABSLvogRbqOWvfRDn4q6hNdbg/mjGjZBp4qCTPnB2mHQQTCKo3oeCUayBCNIBCw==", "type": "package", "path": "humanizer.core.is/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.is.2.14.1.nupkg.sha512", "humanizer.core.is.nuspec", "lib/net6.0/is/Humanizer.resources.dll", "lib/netstandard1.0/is/Humanizer.resources.dll", "lib/netstandard2.0/is/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.it/2.14.1": {"sha512": "jYxGeN4XIKHVND02FZ+Woir3CUTyBhLsqxu9iqR/9BISArkMf1Px6i5pRZnvq4fc5Zn1qw71GKKoCaHDJBsLFw==", "type": "package", "path": "humanizer.core.it/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.it.2.14.1.nupkg.sha512", "humanizer.core.it.nuspec", "lib/net6.0/it/Humanizer.resources.dll", "lib/netstandard1.0/it/Humanizer.resources.dll", "lib/netstandard2.0/it/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.ja/2.14.1": {"sha512": "TM3ablFNoYx4cYJybmRgpDioHpiKSD7q0QtMrmpsqwtiiEsdW5zz/q4PolwAczFnvrKpN6nBXdjnPPKVet93ng==", "type": "package", "path": "humanizer.core.ja/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.ja.2.14.1.nupkg.sha512", "humanizer.core.ja.nuspec", "lib/net6.0/ja/Humanizer.resources.dll", "lib/netstandard1.0/ja/Humanizer.resources.dll", "lib/netstandard2.0/ja/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.ko-KR/2.14.1": {"sha512": "CtvwvK941k/U0r8PGdEuBEMdW6jv/rBiA9tUhakC7Zd2rA/HCnDcbr1DiNZ+/tRshnhzxy/qwmpY8h4qcAYCtQ==", "type": "package", "path": "humanizer.core.ko-kr/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.ko-kr.2.14.1.nupkg.sha512", "humanizer.core.ko-kr.nuspec", "lib/netstandard1.0/ko-KR/Humanizer.resources.dll", "lib/netstandard2.0/ko-KR/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.ku/2.14.1": {"sha512": "vHmzXcVMe+LNrF9txpdHzpG7XJX65SiN9GQd/Zkt6gsGIIEeECHrkwCN5Jnlkddw2M/b0HS4SNxdR1GrSn7uCA==", "type": "package", "path": "humanizer.core.ku/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.ku.2.14.1.nupkg.sha512", "humanizer.core.ku.nuspec", "lib/net6.0/ku/Humanizer.resources.dll", "lib/netstandard1.0/ku/Humanizer.resources.dll", "lib/netstandard2.0/ku/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.lv/2.14.1": {"sha512": "E1/KUVnYBS1bdOTMNDD7LV/jdoZv/fbWTLPtvwdMtSdqLyRTllv6PGM9xVQoFDYlpvVGtEl/09glCojPHw8ffA==", "type": "package", "path": "humanizer.core.lv/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.lv.2.14.1.nupkg.sha512", "humanizer.core.lv.nuspec", "lib/net6.0/lv/Humanizer.resources.dll", "lib/netstandard1.0/lv/Humanizer.resources.dll", "lib/netstandard2.0/lv/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.ms-MY/2.14.1": {"sha512": "vX8oq9HnYmAF7bek4aGgGFJficHDRTLgp/EOiPv9mBZq0i4SA96qVMYSjJ2YTaxs7Eljqit7pfpE2nmBhY5Fnw==", "type": "package", "path": "humanizer.core.ms-my/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.ms-my.2.14.1.nupkg.sha512", "humanizer.core.ms-my.nuspec", "lib/netstandard1.0/ms-MY/Humanizer.resources.dll", "lib/netstandard2.0/ms-MY/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.mt/2.14.1": {"sha512": "pEgTBzUI9hzemF7xrIZigl44LidTUhNu4x/P6M9sAwZjkUF0mMkbpxKkaasOql7lLafKrnszs0xFfaxQyzeuZQ==", "type": "package", "path": "humanizer.core.mt/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.mt.2.14.1.nupkg.sha512", "humanizer.core.mt.nuspec", "lib/netstandard1.0/mt/Humanizer.resources.dll", "lib/netstandard2.0/mt/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.nb/2.14.1": {"sha512": "mbs3m6JJq53ssLqVPxNfqSdTxAcZN3njlG8yhJVx83XVedpTe1ECK9aCa8FKVOXv93Gl+yRHF82Hw9T9LWv2hw==", "type": "package", "path": "humanizer.core.nb/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.nb.2.14.1.nupkg.sha512", "humanizer.core.nb.nuspec", "lib/net6.0/nb/Humanizer.resources.dll", "lib/netstandard1.0/nb/Humanizer.resources.dll", "lib/netstandard2.0/nb/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.nb-NO/2.14.1": {"sha512": "AsJxrrVYmIMbKDGe8W6Z6//wKv9dhWH7RsTcEHSr4tQt/80pcNvLi0hgD3fqfTtg0tWKtgch2cLf4prorEV+5A==", "type": "package", "path": "humanizer.core.nb-no/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.nb-no.2.14.1.nupkg.sha512", "humanizer.core.nb-no.nuspec", "lib/net6.0/nb-NO/Humanizer.resources.dll", "lib/netstandard1.0/nb-NO/Humanizer.resources.dll", "lib/netstandard2.0/nb-NO/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.nl/2.14.1": {"sha512": "24b0OUdzJxfoqiHPCtYnR5Y4l/s4Oh7KW7uDp+qX25NMAHLCGog2eRfA7p2kRJp8LvnynwwQxm2p534V9m55wQ==", "type": "package", "path": "humanizer.core.nl/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.nl.2.14.1.nupkg.sha512", "humanizer.core.nl.nuspec", "lib/net6.0/nl/Humanizer.resources.dll", "lib/netstandard1.0/nl/Humanizer.resources.dll", "lib/netstandard2.0/nl/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.pl/2.14.1": {"sha512": "17mJNYaBssENVZyQHduiq+bvdXS0nhZJGEXtPKoMhKv3GD//WO0mEfd9wjEBsWCSmWI7bjRqhCidxzN+YtJmsg==", "type": "package", "path": "humanizer.core.pl/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.pl.2.14.1.nupkg.sha512", "humanizer.core.pl.nuspec", "lib/net6.0/pl/Humanizer.resources.dll", "lib/netstandard1.0/pl/Humanizer.resources.dll", "lib/netstandard2.0/pl/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.pt/2.14.1": {"sha512": "8HB8qavcVp2la1GJX6t+G9nDYtylPKzyhxr9LAooIei9MnQvNsjEiIE4QvHoeDZ4weuQ9CsPg1c211XUMVEZ4A==", "type": "package", "path": "humanizer.core.pt/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.pt.2.14.1.nupkg.sha512", "humanizer.core.pt.nuspec", "lib/net6.0/pt/Humanizer.resources.dll", "lib/netstandard1.0/pt/Humanizer.resources.dll", "lib/netstandard2.0/pt/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.ro/2.14.1": {"sha512": "psXNOcA6R8fSHoQYhpBTtTTYiOk8OBoN3PKCEDgsJKIyeY5xuK81IBdGi77qGZMu/OwBRQjQCBMtPJb0f4O1+A==", "type": "package", "path": "humanizer.core.ro/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.ro.2.14.1.nupkg.sha512", "humanizer.core.ro.nuspec", "lib/net6.0/ro/Humanizer.resources.dll", "lib/netstandard1.0/ro/Humanizer.resources.dll", "lib/netstandard2.0/ro/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.ru/2.14.1": {"sha512": "zm245xUWrajSN2t9H7BTf84/2APbUkKlUJpcdgsvTdAysr1ag9fi1APu6JEok39RRBXDfNRVZHawQ/U8X0pSvQ==", "type": "package", "path": "humanizer.core.ru/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.ru.2.14.1.nupkg.sha512", "humanizer.core.ru.nuspec", "lib/net6.0/ru/Humanizer.resources.dll", "lib/netstandard1.0/ru/Humanizer.resources.dll", "lib/netstandard2.0/ru/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.sk/2.14.1": {"sha512": "Ncw24Vf3ioRnbU4MsMFHafkyYi8JOnTqvK741GftlQvAbULBoTz2+e7JByOaasqeSi0KfTXeegJO+5Wk1c0Mbw==", "type": "package", "path": "humanizer.core.sk/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.sk.2.14.1.nupkg.sha512", "humanizer.core.sk.nuspec", "lib/net6.0/sk/Humanizer.resources.dll", "lib/netstandard1.0/sk/Humanizer.resources.dll", "lib/netstandard2.0/sk/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.sl/2.14.1": {"sha512": "l8sUy4ciAIbVThWNL0atzTS2HWtv8qJrsGWNlqrEKmPwA4SdKolSqnTes9V89fyZTc2Q43jK8fgzVE2C7t009A==", "type": "package", "path": "humanizer.core.sl/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.sl.2.14.1.nupkg.sha512", "humanizer.core.sl.nuspec", "lib/net6.0/sl/Humanizer.resources.dll", "lib/netstandard1.0/sl/Humanizer.resources.dll", "lib/netstandard2.0/sl/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.sr/2.14.1": {"sha512": "rnNvhpkOrWEymy7R/MiFv7uef8YO5HuXDyvojZ7JpijHWA5dXuVXooCOiA/3E93fYa3pxDuG2OQe4M/olXbQ7w==", "type": "package", "path": "humanizer.core.sr/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.sr.2.14.1.nupkg.sha512", "humanizer.core.sr.nuspec", "lib/net6.0/sr/Humanizer.resources.dll", "lib/netstandard1.0/sr/Humanizer.resources.dll", "lib/netstandard2.0/sr/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.sr-Latn/2.14.1": {"sha512": "nuy/ykpk974F8ItoQMS00kJPr2dFNjOSjgzCwfysbu7+gjqHmbLcYs7G4kshLwdA4AsVncxp99LYeJgoh1JF5g==", "type": "package", "path": "humanizer.core.sr-latn/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.sr-latn.2.14.1.nupkg.sha512", "humanizer.core.sr-latn.nuspec", "lib/net6.0/sr-Latn/Humanizer.resources.dll", "lib/netstandard1.0/sr-Latn/Humanizer.resources.dll", "lib/netstandard2.0/sr-Latn/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.sv/2.14.1": {"sha512": "E53+tpAG0RCp+cSSI7TfBPC+NnsEqUuoSV0sU+rWRXWr9MbRWx1+Zj02XMojqjGzHjjOrBFBBio6m74seFl0AA==", "type": "package", "path": "humanizer.core.sv/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.sv.2.14.1.nupkg.sha512", "humanizer.core.sv.nuspec", "lib/net6.0/sv/Humanizer.resources.dll", "lib/netstandard1.0/sv/Humanizer.resources.dll", "lib/netstandard2.0/sv/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.th-TH/2.14.1": {"sha512": "eSevlJtvs1r4vQarNPfZ2kKDp/xMhuD00tVVzRXkSh1IAZbBJI/x2ydxUOwfK9bEwEp+YjvL1Djx2+kw7ziu7g==", "type": "package", "path": "humanizer.core.th-th/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.th-th.2.14.1.nupkg.sha512", "humanizer.core.th-th.nuspec", "lib/netstandard1.0/th-TH/Humanizer.resources.dll", "lib/netstandard2.0/th-TH/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.tr/2.14.1": {"sha512": "rQ8N+o7yFcFqdbtu1mmbrXFi8TQ+uy+fVH9OPI0CI3Cu1om5hUU/GOMC3hXsTCI6d79y4XX+0HbnD7FT5khegA==", "type": "package", "path": "humanizer.core.tr/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.tr.2.14.1.nupkg.sha512", "humanizer.core.tr.nuspec", "lib/net6.0/tr/Humanizer.resources.dll", "lib/netstandard1.0/tr/Humanizer.resources.dll", "lib/netstandard2.0/tr/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.uk/2.14.1": {"sha512": "2uEfujwXKNm6bdpukaLtEJD+04uUtQD65nSGCetA1fYNizItEaIBUboNfr3GzJxSMQotNwGVM3+nSn8jTd0VSg==", "type": "package", "path": "humanizer.core.uk/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.uk.2.14.1.nupkg.sha512", "humanizer.core.uk.nuspec", "lib/net6.0/uk/Humanizer.resources.dll", "lib/netstandard1.0/uk/Humanizer.resources.dll", "lib/netstandard2.0/uk/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"sha512": "TD3ME2sprAvFqk9tkWrvSKx5XxEMlAn1sjk+cYClSWZlIMhQQ2Bp/w0VjX1Kc5oeKjxRAnR7vFcLUFLiZIDk9Q==", "type": "package", "path": "humanizer.core.uz-cyrl-uz/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.uz-cyrl-uz.2.14.1.nupkg.sha512", "humanizer.core.uz-cyrl-uz.nuspec", "lib/net6.0/uz-Cyrl-UZ/Humanizer.resources.dll", "lib/netstandard1.0/uz-Cyrl-UZ/Humanizer.resources.dll", "lib/netstandard2.0/uz-Cyrl-UZ/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"sha512": "/kHAoF4g0GahnugZiEMpaHlxb+W6jCEbWIdsq9/I1k48ULOsl/J0pxZj93lXC3omGzVF1BTVIeAtv5fW06Phsg==", "type": "package", "path": "humanizer.core.uz-latn-uz/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.uz-latn-uz.2.14.1.nupkg.sha512", "humanizer.core.uz-latn-uz.nuspec", "lib/net6.0/uz-Latn-UZ/Humanizer.resources.dll", "lib/netstandard1.0/uz-Latn-UZ/Humanizer.resources.dll", "lib/netstandard2.0/uz-Latn-UZ/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.vi/2.14.1": {"sha512": "rsQNh9rmHMBtnsUUlJbShMsIMGflZtPmrMM6JNDw20nhsvqfrdcoDD8cMnLAbuSovtc3dP+swRmLQzKmXDTVPA==", "type": "package", "path": "humanizer.core.vi/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.vi.2.14.1.nupkg.sha512", "humanizer.core.vi.nuspec", "lib/net6.0/vi/Humanizer.resources.dll", "lib/netstandard1.0/vi/Humanizer.resources.dll", "lib/netstandard2.0/vi/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.zh-CN/2.14.1": {"sha512": "uH2dWhrgugkCjDmduLdAFO9w1Mo0q07EuvM0QiIZCVm6FMCu/lGv2fpMu4GX+4HLZ6h5T2Pg9FIdDLCPN2a67w==", "type": "package", "path": "humanizer.core.zh-cn/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.zh-cn.2.14.1.nupkg.sha512", "humanizer.core.zh-cn.nuspec", "lib/net6.0/zh-CN/Humanizer.resources.dll", "lib/netstandard1.0/zh-CN/Humanizer.resources.dll", "lib/netstandard2.0/zh-CN/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.zh-Hans/2.14.1": {"sha512": "WH6IhJ8V1UBG7rZXQk3dZUoP2gsi8a0WkL8xL0sN6WGiv695s8nVcmab9tWz20ySQbuzp0UkSxUQFi5jJHIpOQ==", "type": "package", "path": "humanizer.core.zh-hans/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.zh-hans.2.14.1.nupkg.sha512", "humanizer.core.zh-hans.nuspec", "lib/net6.0/zh-Hans/Humanizer.resources.dll", "lib/netstandard1.0/zh-Hans/Humanizer.resources.dll", "lib/netstandard2.0/zh-Hans/Humanizer.resources.dll", "logo.png"]}, "Humanizer.Core.zh-Hant/2.14.1": {"sha512": "VIXB7HCUC34OoaGnO3HJVtSv2/wljPhjV7eKH4+TFPgQdJj2lvHNKY41Dtg0Bphu7X5UaXFR4zrYYyo+GNOjbA==", "type": "package", "path": "humanizer.core.zh-hant/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.zh-hant.2.14.1.nupkg.sha512", "humanizer.core.zh-hant.nuspec", "lib/net6.0/zh-Hant/Humanizer.resources.dll", "lib/netstandard1.0/zh-Hant/Humanizer.resources.dll", "lib/netstandard2.0/zh-Hant/Humanizer.resources.dll", "logo.png"]}, "Microsoft.AspNetCore.Razor.Language/6.0.24": {"sha512": "kBL6ljTREp/3fk8EKN27mrPy3WTqWUjiqCkKFlCKHUKRO3/9rAasKizX3vPWy4ZTcNsIPmVWUHwjDFmiW4MyNA==", "type": "package", "path": "microsoft.aspnetcore.razor.language/6.0.24", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll", "microsoft.aspnetcore.razor.language.6.0.24.nupkg.sha512", "microsoft.aspnetcore.razor.language.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"sha512": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Build/17.8.3": {"sha512": "jOxP2DrBZb2zuDO5M8LfI50SCdXlahgUHJ6mH0jz4OBID0F9o+DVggk0CPAONmcbUPo2SsQCFkMaxmHkKLj99Q==", "type": "package", "path": "microsoft.build/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.dll", "lib/net472/Microsoft.Build.pdb", "lib/net472/Microsoft.Build.xml", "lib/net8.0/Microsoft.Build.dll", "lib/net8.0/Microsoft.Build.pdb", "lib/net8.0/Microsoft.Build.xml", "microsoft.build.17.8.3.nupkg.sha512", "microsoft.build.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.dll", "ref/net472/Microsoft.Build.xml", "ref/net8.0/Microsoft.Build.dll", "ref/net8.0/Microsoft.Build.xml"]}, "Microsoft.Build.Framework/17.8.3": {"sha512": "NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "type": "package", "path": "microsoft.build.framework/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.Framework.dll", "lib/net472/Microsoft.Build.Framework.pdb", "lib/net472/Microsoft.Build.Framework.xml", "lib/net8.0/Microsoft.Build.Framework.dll", "lib/net8.0/Microsoft.Build.Framework.pdb", "lib/net8.0/Microsoft.Build.Framework.xml", "microsoft.build.framework.17.8.3.nupkg.sha512", "microsoft.build.framework.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.Framework.dll", "ref/net472/Microsoft.Build.Framework.xml", "ref/net8.0/Microsoft.Build.Framework.dll", "ref/net8.0/Microsoft.Build.Framework.xml", "ref/netstandard2.0/Microsoft.Build.Framework.dll", "ref/netstandard2.0/Microsoft.Build.Framework.xml"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"sha512": "AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.props", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets", "buildTransitive/config/analysislevel_2_9_8_all.globalconfig", "buildTransitive/config/analysislevel_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_all.globalconfig", "buildTransitive/config/analysislevel_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_default.globalconfig", "buildTransitive/config/analysislevel_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_none.globalconfig", "buildTransitive/config/analysislevel_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_all.globalconfig", "buildTransitive/config/analysislevel_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_default.globalconfig", "buildTransitive/config/analysislevel_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_none.globalconfig", "buildTransitive/config/analysislevel_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended_warnaserror.globalconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"sha512": "gyQ70pJ4T7hu/s0+QnEaXtYfeG/JrttGnxHJlrhpxsQjRIUGuRhVwNBtkHHYOrUAZ/l47L98/NiJX6QmTwAyrg==", "type": "package", "path": "microsoft.codeanalysis.analyzerutilities/3.3.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.xml", "microsoft.codeanalysis.analyzerutilities.3.3.0.nupkg.sha512", "microsoft.codeanalysis.analyzerutilities.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.8.0": {"sha512": "/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "type": "package", "path": "microsoft.codeanalysis.common/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.dll", "lib/net6.0/Microsoft.CodeAnalysis.pdb", "lib/net6.0/Microsoft.CodeAnalysis.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.dll", "lib/net7.0/Microsoft.CodeAnalysis.pdb", "lib/net7.0/Microsoft.CodeAnalysis.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"sha512": "+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.CSharp.Features/4.8.0": {"sha512": "Gpas3l8PE1xz1VDIJNMkYuoFPXtuALxybP04caXh9avC2a0elsoBdukndkJXVZgdKPwraf0a98s7tjqnEk5QIQ==", "type": "package", "path": "microsoft.codeanalysis.csharp.features/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Features.dll", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Features.pdb", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Features.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Features.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Features.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Features.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Features.resources.dll", "microsoft.codeanalysis.csharp.features.4.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.features.nuspec"]}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"sha512": "3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "type": "package", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.workspaces.nuspec"]}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"sha512": "r12elUp4MRjdnRfxEP+xqVSUUfG3yIJTBEJGwbfvF5oU4m0jb9HC0gFG28V/dAkYGMkRmHVi3qvrnBLQSw9X3Q==", "type": "package", "path": "microsoft.codeanalysis.elfie/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.CodeAnalysis.Elfie.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Elfie.dll", "microsoft.codeanalysis.elfie.1.0.0.nupkg.sha512", "microsoft.codeanalysis.elfie.nuspec"]}, "Microsoft.CodeAnalysis.Features/4.8.0": {"sha512": "sCVzMtSETGE16KeScwwlVfxaKRbUMSf/cgRPRPMJuou37SLT7XkIBzJu4e7mlFTzpJbfalV5tOcKpUtLO3eJAg==", "type": "package", "path": "microsoft.codeanalysis.features/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.Features.dll", "lib/net6.0/Microsoft.CodeAnalysis.Features.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Features.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.Features.dll", "lib/net7.0/Microsoft.CodeAnalysis.Features.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Features.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Features.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Features.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Features.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Features.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Features.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Features.resources.dll", "microsoft.codeanalysis.features.4.8.0.nupkg.sha512", "microsoft.codeanalysis.features.nuspec"]}, "Microsoft.CodeAnalysis.Razor/6.0.24": {"sha512": "xIAjR6l/1PO2ILT6/lOGYfe8OzMqfqxh1lxFuM4Exluwc2sQhJw0kS7pEyJ0DE/UMYu6Jcdc53DmjOxQUDT2Pg==", "type": "package", "path": "microsoft.codeanalysis.razor/6.0.24", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll", "microsoft.codeanalysis.razor.6.0.24.nupkg.sha512", "microsoft.codeanalysis.razor.nuspec"]}, "Microsoft.CodeAnalysis.Scripting.Common/4.8.0": {"sha512": "ysiNNbAASVhV9wEd5oY2x99EwaVYtB13XZRjHsgWT/R1mQkxZF8jWsf7JWaZxD1+jNoz1QCQ6nbe+vr+6QvlFA==", "type": "package", "path": "microsoft.codeanalysis.scripting.common/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.Scripting.dll", "lib/net6.0/Microsoft.CodeAnalysis.Scripting.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Scripting.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.Scripting.dll", "lib/net7.0/Microsoft.CodeAnalysis.Scripting.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Scripting.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Scripting.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Scripting.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Scripting.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Scripting.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll", "microsoft.codeanalysis.scripting.common.4.8.0.nupkg.sha512", "microsoft.codeanalysis.scripting.common.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"sha512": "LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "type": "package", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.common.nuspec"]}, "Microsoft.CSharp/4.5.0": {"sha512": "kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "type": "package", "path": "microsoft.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.5.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/5.1.5": {"sha512": "6kvhQjY5uBCdBccezFD2smfnpQjQ33cZtUZVrNvxlwoBu6uopM5INH6uSgLI7JRLtlQ3bMPwnhMq4kchsXeZ5w==", "type": "package", "path": "microsoft.data.sqlclient/5.1.5", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.pdb", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/Microsoft.Data.SqlClient.dll", "lib/net6.0/Microsoft.Data.SqlClient.pdb", "lib/net6.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.5.1.5.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.pdb", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net6.0/Microsoft.Data.SqlClient.dll", "ref/net6.0/Microsoft.Data.SqlClient.pdb", "ref/net6.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb"]}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"sha512": "wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.DiaSymReader/2.0.0": {"sha512": "QcZrCETsBJqy/vQpFtJc+jSXQ0K5sucQ6NUFbTNVHD4vfZZOwjZ/3sBzczkC4DityhD3AVO/+K/+9ioLs1AgRA==", "type": "package", "path": "microsoft.diasymreader/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.DiaSymReader.dll", "lib/netstandard2.0/Microsoft.DiaSymReader.pdb", "lib/netstandard2.0/Microsoft.DiaSymReader.xml", "microsoft.diasymreader.2.0.0.nupkg.sha512", "microsoft.diasymreader.nuspec"]}, "Microsoft.DotNet.Scaffolding.Shared/8.0.7": {"sha512": "q11ADAFXiDHsiH6YpHDCtu/5JB/k6nBWM1pwoI3yOzq//2ZdnWgNkgs1coZyv/XZluxZbVmeo6f+ZoYGebIj/Q==", "type": "package", "path": "microsoft.dotnet.scaffolding.shared/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "lib/netstandard2.0/Microsoft.DotNet.Scaffolding.Shared.dll", "lib/netstandard2.0/Microsoft.DotNet.Scaffolding.Shared.xml", "microsoft.dotnet.scaffolding.shared.8.0.7.nupkg.sha512", "microsoft.dotnet.scaffolding.shared.nuspec"]}, "Microsoft.EntityFrameworkCore/8.0.11": {"sha512": "stbjWBTtpQ1HtqXMFyKnXFTr76PvaOHI2b2h85JqBi3eZr00nspvR/a90Zwh8CQ4rVawqLiTG0+0yZQWaav+sQ==", "type": "package", "path": "microsoft.entityframeworkcore/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.8.0.11.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.11": {"sha512": "++zY0Ea724ku1jptWJmF7jm3I4IXTexfT4qi1ETcSFFF7qj+qm6rRgN7mTuKkwIETuXk0ikfzudryRjUGrrNKQ==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.8.0.11.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.11": {"sha512": "NI/AJQjtC7qgWM8Nr85sRkwlog2AnFer5RKP8xTUH0RuPF3nN0tGXBEeYJOLZWp+/+M/C6O7MMDRhKRE8bZwIA==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.8.0.11.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/8.0.11": {"sha512": "KxOvpbaKiUmbLvenr0T/4F1Vdm0Sq+iajLbesQK7/WKB/Dx+FQHCZ0f5jCXrVWK2QKF9eHzQ5JPA1L6hcb25FQ==", "type": "package", "path": "microsoft.entityframeworkcore.design/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net8.0/Microsoft.EntityFrameworkCore.Design.props", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.8.0.11.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/8.0.11": {"sha512": "3TuuW3i5I4Ro0yoaHmi2MqEDGObOVuhLaMEnd/heaLB1fcvm4fu4PevmC4BOWnI0vo176AIlV5o4rEQciLoohw==", "type": "package", "path": "microsoft.entityframeworkcore.relational/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.8.0.11.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.5": {"sha512": "vBJR6QzJrZvd6oHsfMXA++v+dKYx/vhoPDgUgQZwKbcP9oYJowzoWl5JydPjlrCjwq4sxIOyqxJ7m/rSsb7zMg==", "type": "package", "path": "microsoft.entityframeworkcore.sqlserver/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.xml", "microsoft.entityframeworkcore.sqlserver.8.0.5.nupkg.sha512", "microsoft.entityframeworkcore.sqlserver.nuspec"]}, "Microsoft.EntityFrameworkCore.Tools/8.0.11": {"sha512": "9iUOj0npm2FxOkGIE3ktv0N0YU1oEhaMTJoDYuKS8dGNkWo1CPm7RjsoJABesKFk1lkCIfTE5SHXb45GIMjDnQ==", "type": "package", "path": "microsoft.entityframeworkcore.tools/8.0.11", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "docs/PACKAGE.md", "lib/net8.0/_._", "microsoft.entityframeworkcore.tools.8.0.11.nupkg.sha512", "microsoft.entityframeworkcore.tools.nuspec", "tools/EntityFrameworkCore.PS2.psd1", "tools/EntityFrameworkCore.PS2.psm1", "tools/EntityFrameworkCore.psd1", "tools/EntityFrameworkCore.psm1", "tools/about_EntityFrameworkCore.help.txt", "tools/init.ps1", "tools/net461/any/ef.exe", "tools/net461/win-arm64/ef.exe", "tools/net461/win-x86/ef.exe", "tools/netcoreapp2.0/any/ef.dll", "tools/netcoreapp2.0/any/ef.runtimeconfig.json"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"sha512": "HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"sha512": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"sha512": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"sha512": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"sha512": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/8.0.2": {"sha512": "mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "type": "package", "path": "microsoft.extensions.dependencymodel/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.1": {"sha512": "4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "type": "package", "path": "microsoft.extensions.logging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.1.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"sha512": "nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.2": {"sha512": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "type": "package", "path": "microsoft.extensions.options/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.56.0": {"sha512": "rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "type": "package", "path": "microsoft.identity.client/4.56.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid10.0/Microsoft.Identity.Client.dll", "lib/monoandroid10.0/Microsoft.Identity.Client.xml", "lib/monoandroid90/Microsoft.Identity.Client.dll", "lib/monoandroid90/Microsoft.Identity.Client.xml", "lib/net45/Microsoft.Identity.Client.dll", "lib/net45/Microsoft.Identity.Client.xml", "lib/net461/Microsoft.Identity.Client.dll", "lib/net461/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0-windows7.0/Microsoft.Identity.Client.dll", "lib/net6.0-windows7.0/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "lib/uap10.0.17763/Microsoft.Identity.Client.dll", "lib/uap10.0.17763/Microsoft.Identity.Client.pri", "lib/uap10.0.17763/Microsoft.Identity.Client.xml", "lib/xamarinios10/Microsoft.Identity.Client.dll", "lib/xamarinios10/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.56.0.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"sha512": "H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.56.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"sha512": "xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"sha512": "9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.35.0": {"sha512": "jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "type": "package", "path": "microsoft.identitymodel.logging/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/6.35.0": {"sha512": "BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "type": "package", "path": "microsoft.identitymodel.protocols/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"sha512": "LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.35.0": {"sha512": "RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "type": "package", "path": "microsoft.identitymodel.tokens/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NET.StringTools/17.8.3": {"sha512": "y6DiuacjlIfXH3XVQG5htf+4oheinZAo7sHbITB3z7yCXQec48f9ZhGSXkr+xn1bfl73Yc3ZQEW2peJ5X68AvQ==", "type": "package", "path": "microsoft.net.stringtools/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.NET.StringTools.dll", "lib/net472/Microsoft.NET.StringTools.pdb", "lib/net472/Microsoft.NET.StringTools.xml", "lib/net8.0/Microsoft.NET.StringTools.dll", "lib/net8.0/Microsoft.NET.StringTools.pdb", "lib/net8.0/Microsoft.NET.StringTools.xml", "lib/netstandard2.0/Microsoft.NET.StringTools.dll", "lib/netstandard2.0/Microsoft.NET.StringTools.pdb", "lib/netstandard2.0/Microsoft.NET.StringTools.xml", "microsoft.net.stringtools.17.8.3.nupkg.sha512", "microsoft.net.stringtools.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.NET.StringTools.dll", "ref/net472/Microsoft.NET.StringTools.xml", "ref/net8.0/Microsoft.NET.StringTools.dll", "ref/net8.0/Microsoft.NET.StringTools.xml", "ref/netstandard2.0/Microsoft.NET.StringTools.dll", "ref/netstandard2.0/Microsoft.NET.StringTools.xml"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.VisualStudio.Web.CodeGeneration/8.0.7": {"sha512": "8WqtBA1DxiiEq1c2PjgkeFYHPC0WwC5Yl7FuDg41RQ6rvi661mSHCApzz3/rp/cfb8I7DiG1+3d01q5ck+BT1w==", "type": "package", "path": "microsoft.visualstudio.web.codegeneration/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "README.md", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.dll", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.xml", "microsoft.visualstudio.web.codegeneration.8.0.7.nupkg.sha512", "microsoft.visualstudio.web.codegeneration.nuspec"]}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/8.0.7": {"sha512": "kahoV4hImS/uUfn3K9ecU+o1U4pn5sdnabG+DGsK9bf6gL0tw8MnrArz3M9kWYMKDsVd3/j/3ZriTH+hVrPW2w==", "type": "package", "path": "microsoft.visualstudio.web.codegeneration.core/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "README.md", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.xml", "microsoft.visualstudio.web.codegeneration.core.8.0.7.nupkg.sha512", "microsoft.visualstudio.web.codegeneration.core.nuspec"]}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/8.0.7": {"sha512": "qURp+VCwreGNQBofL6z3uUnLmrG3PCap/jN/B8jGqsiMLjhR+FOO7r9R1vcNeXYd5cFORpQvsuZi6GWXxr2dCA==", "type": "package", "path": "microsoft.visualstudio.web.codegeneration.design/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "README.md", "lib/net8.0/dotnet-aspnet-codegenerator-design.dll", "lib/net8.0/dotnet-aspnet-codegenerator-design.xml", "microsoft.visualstudio.web.codegeneration.design.8.0.7.nupkg.sha512", "microsoft.visualstudio.web.codegeneration.design.nuspec"]}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/8.0.7": {"sha512": "Cd37PMogJ7sbzbUkbZsqPOLpKg0xbBLypsGuJ8HLX7caTDSg2NIsNT/MJKu0VybfoQ2L4fli8XpJXxVytbx1wA==", "type": "package", "path": "microsoft.visualstudio.web.codegeneration.entityframeworkcore/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "README.md", "Templates/DbContext/NewLocalDbContext.cshtml", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.runtimeconfig.json", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.xml", "microsoft.visualstudio.web.codegeneration.entityframeworkcore.8.0.7.nupkg.sha512", "microsoft.visualstudio.web.codegeneration.entityframeworkcore.nuspec"]}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/8.0.7": {"sha512": "uXtCSh13HD7589QSez9cVwQ2xacIgDuf33/ifW8a6DqxC+dF5ium2p+5odUNbzRU8cnCuISjti7F+D4F9kLLVw==", "type": "package", "path": "microsoft.visualstudio.web.codegeneration.templating/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "README.md", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.xml", "microsoft.visualstudio.web.codegeneration.templating.8.0.7.nupkg.sha512", "microsoft.visualstudio.web.codegeneration.templating.nuspec"]}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/8.0.7": {"sha512": "2oA20npXOXxH2pmnTtXNrPgow+W3dGREI+SWrCci5IbTgQE6NOhL+5PmbxqAqjhmoyy9CSEbaav9eWTM7jEdQQ==", "type": "package", "path": "microsoft.visualstudio.web.codegeneration.utils/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "README.md", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.xml", "microsoft.visualstudio.web.codegeneration.utils.8.0.7.nupkg.sha512", "microsoft.visualstudio.web.codegeneration.utils.nuspec"]}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/8.0.7": {"sha512": "IN7xeBHZ/WtYN62Ez0rEWqpsiTlpEUbGcjvXLRF2gNO8SdhFn9IhzdixKXTLzHTf89oS9Y5mFIPkWcU4Y99USA==", "type": "package", "path": "microsoft.visualstudio.web.codegenerators.mvc/8.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Generators/ParameterDefinitions/area.json", "Generators/ParameterDefinitions/blazor-identity.json", "Generators/ParameterDefinitions/blazor.json", "Generators/ParameterDefinitions/controller.json", "Generators/ParameterDefinitions/identity.json", "Generators/ParameterDefinitions/minimalapi.json", "Generators/ParameterDefinitions/razorpage.json", "Generators/ParameterDefinitions/view.json", "Icon.png", "NOTICE.txt", "README.md", "Templates/Blazor/Create.tt", "Templates/Blazor/Delete.tt", "Templates/Blazor/Details.tt", "Templates/Blazor/Edit.tt", "Templates/Blazor/Index.tt", "Templates/BlazorIdentity/IdentityComponentsEndpointRouteBuilderExtensions.tt", "Templates/BlazorIdentity/IdentityNoOpEmailSender.tt", "Templates/BlazorIdentity/IdentityRedirectManager.tt", "Templates/BlazorIdentity/IdentityRevalidatingAuthenticationStateProvider.tt", "Templates/BlazorIdentity/IdentityUserAccessor.tt", "Templates/BlazorIdentity/Pages/ConfirmEmail.tt", "Templates/BlazorIdentity/Pages/ConfirmEmailChange.tt", "Templates/BlazorIdentity/Pages/ExternalLogin.tt", "Templates/BlazorIdentity/Pages/ForgotPassword.tt", "Templates/BlazorIdentity/Pages/ForgotPasswordConfirmation.tt", "Templates/BlazorIdentity/Pages/InvalidPasswordReset.tt", "Templates/BlazorIdentity/Pages/InvalidUser.tt", "Templates/BlazorIdentity/Pages/Lockout.tt", "Templates/BlazorIdentity/Pages/Login.tt", "Templates/BlazorIdentity/Pages/LoginWith2fa.tt", "Templates/BlazorIdentity/Pages/LoginWithRecoveryCode.tt", "Templates/BlazorIdentity/Pages/Manage/ChangePassword.tt", "Templates/BlazorIdentity/Pages/Manage/DeletePersonalData.tt", "Templates/BlazorIdentity/Pages/Manage/Disable2fa.tt", "Templates/BlazorIdentity/Pages/Manage/Email.tt", "Templates/BlazorIdentity/Pages/Manage/EnableAuthenticator.tt", "Templates/BlazorIdentity/Pages/Manage/ExternalLogins.tt", "Templates/BlazorIdentity/Pages/Manage/GenerateRecoveryCodes.tt", "Templates/BlazorIdentity/Pages/Manage/Index.tt", "Templates/BlazorIdentity/Pages/Manage/PersonalData.tt", "Templates/BlazorIdentity/Pages/Manage/ResetAuthenticator.tt", "Templates/BlazorIdentity/Pages/Manage/SetPassword.tt", "Templates/BlazorIdentity/Pages/Manage/TwoFactorAuthentication.tt", "Templates/BlazorIdentity/Pages/Manage/_Imports.tt", "Templates/BlazorIdentity/Pages/Register.tt", "Templates/BlazorIdentity/Pages/RegisterConfirmation.tt", "Templates/BlazorIdentity/Pages/ResendEmailConfirmation.tt", "Templates/BlazorIdentity/Pages/ResetPassword.tt", "Templates/BlazorIdentity/Pages/ResetPasswordConfirmation.tt", "Templates/BlazorIdentity/Pages/_Imports.tt", "Templates/BlazorIdentity/Shared/AccountLayout.tt", "Templates/BlazorIdentity/Shared/ExternalLoginPicker.tt", "Templates/BlazorIdentity/Shared/ManageLayout.tt", "Templates/BlazorIdentity/Shared/ManageNavMenu.tt", "Templates/BlazorIdentity/Shared/RedirectToLogin.tt", "Templates/BlazorIdentity/Shared/ShowRecoveryCodes.tt", "Templates/BlazorIdentity/Shared/StatusMessage.tt", "Templates/ControllerGenerator/ApiControllerWithContext.cshtml", "Templates/ControllerGenerator/MvcControllerWithContext.cshtml", "Templates/General/IdentityApplicationUser.Interfaces.cs", "Templates/General/IdentityApplicationUser.cs", "Templates/General/IdentityApplicationUser.tt", "Templates/General/IdentityApplicationUserModel.cs", "Templates/General/IdentityDbContext.Interfaces.cs", "Templates/General/IdentityDbContext.cs", "Templates/General/IdentityDbContext.tt", "Templates/General/IdentityDbContextModel.cs", "Templates/Identity/Data/ApplicationDbContext.cshtml", "Templates/Identity/Data/ApplicationUser.cshtml", "Templates/Identity/IdentityHostingStartup.cshtml", "Templates/Identity/Pages/Account/Account.AccessDenied.cs.cshtml", "Templates/Identity/Pages/Account/Account.AccessDenied.cshtml", "Templates/Identity/Pages/Account/Account.ConfirmEmail.cs.cshtml", "Templates/Identity/Pages/Account/Account.ConfirmEmail.cshtml", "Templates/Identity/Pages/Account/Account.ConfirmEmailChange.cs.cshtml", "Templates/Identity/Pages/Account/Account.ConfirmEmailChange.cshtml", "Templates/Identity/Pages/Account/Account.ExternalLogin.cs.cshtml", "Templates/Identity/Pages/Account/Account.ExternalLogin.cshtml", "Templates/Identity/Pages/Account/Account.ForgotPassword.cs.cshtml", "Templates/Identity/Pages/Account/Account.ForgotPassword.cshtml", "Templates/Identity/Pages/Account/Account.ForgotPasswordConfirmation.cs.cshtml", "Templates/Identity/Pages/Account/Account.ForgotPasswordConfirmation.cshtml", "Templates/Identity/Pages/Account/Account.Lockout.cs.cshtml", "Templates/Identity/Pages/Account/Account.Lockout.cshtml", "Templates/Identity/Pages/Account/Account.Login.cs.cshtml", "Templates/Identity/Pages/Account/Account.Login.cshtml", "Templates/Identity/Pages/Account/Account.LoginWith2fa.cs.cshtml", "Templates/Identity/Pages/Account/Account.LoginWith2fa.cshtml", "Templates/Identity/Pages/Account/Account.LoginWithRecoveryCode.cs.cshtml", "Templates/Identity/Pages/Account/Account.LoginWithRecoveryCode.cshtml", "Templates/Identity/Pages/Account/Account.Logout.cs.cshtml", "Templates/Identity/Pages/Account/Account.Logout.cshtml", "Templates/Identity/Pages/Account/Account.Register.cs.cshtml", "Templates/Identity/Pages/Account/Account.Register.cshtml", "Templates/Identity/Pages/Account/Account.RegisterConfirmation.cs.cshtml", "Templates/Identity/Pages/Account/Account.RegisterConfirmation.cshtml", "Templates/Identity/Pages/Account/Account.ResendEmailConfirmation.cs.cshtml", "Templates/Identity/Pages/Account/Account.ResendEmailConfirmation.cshtml", "Templates/Identity/Pages/Account/Account.ResetPassword.cs.cshtml", "Templates/Identity/Pages/Account/Account.ResetPassword.cshtml", "Templates/Identity/Pages/Account/Account.ResetPasswordConfirmation.cs.cshtml", "Templates/Identity/Pages/Account/Account.ResetPasswordConfirmation.cshtml", "Templates/Identity/Pages/Account/Account._StatusMessage.cshtml", "Templates/Identity/Pages/Account/Account._ViewImports.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ChangePassword.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ChangePassword.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.DeletePersonalData.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.DeletePersonalData.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.Disable2fa.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.Disable2fa.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.DownloadPersonalData.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.DownloadPersonalData.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.Email.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.Email.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.EnableAuthenticator.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.EnableAuthenticator.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ExternalLogins.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ExternalLogins.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.GenerateRecoveryCodes.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.GenerateRecoveryCodes.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.Index.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.Index.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ManageNavPages.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.PersonalData.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.PersonalData.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ResetAuthenticator.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ResetAuthenticator.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.SetPassword.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.SetPassword.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ShowRecoveryCodes.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.ShowRecoveryCodes.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.TwoFactorAuthentication.cs.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage.TwoFactorAuthentication.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage._Layout.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage._ManageNav.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage._StatusMessage.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage._ViewImports.cshtml", "Templates/Identity/Pages/Account/Manage/Account.Manage._ViewStart.cshtml", "Templates/Identity/Pages/Error.cs.cshtml", "Templates/Identity/Pages/Error.cshtml", "Templates/Identity/Pages/_Layout.cshtml", "Templates/Identity/Pages/_ValidationScriptsPartial.cshtml", "Templates/Identity/Pages/_ViewImports.cshtml", "Templates/Identity/Pages/_ViewStart.cshtml", "Templates/Identity/ScaffoldingReadme.cshtml", "Templates/Identity/SupportPages._CookieConsentPartial.cshtml", "Templates/Identity/SupportPages._ViewImports.cshtml", "Templates/Identity/SupportPages._ViewStart.cshtml", "Templates/Identity/_LoginPartial.cshtml", "Templates/Identity/wwwroot/css/site.css", "Templates/Identity/wwwroot/favicon.ico", "Templates/Identity/wwwroot/js/site.js", "Templates/Identity/wwwroot/lib/bootstrap/LICENSE", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Templates/Identity/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "Templates/Identity/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "Templates/Identity/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "Templates/Identity/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Templates/Identity/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Templates/Identity/wwwroot/lib/jquery-validation/LICENSE.md", "Templates/Identity/wwwroot/lib/jquery-validation/dist/additional-methods.js", "Templates/Identity/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "Templates/Identity/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "Templates/Identity/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "Templates/Identity/wwwroot/lib/jquery/LICENSE.txt", "Templates/Identity/wwwroot/lib/jquery/dist/jquery.js", "Templates/Identity/wwwroot/lib/jquery/dist/jquery.min.js", "Templates/Identity/wwwroot/lib/jquery/dist/jquery.min.map", "Templates/Identity_Versioned/Bootstrap4/Data/ApplicationDbContext.cshtml", "Templates/Identity_Versioned/Bootstrap4/Data/ApplicationUser.cshtml", "Templates/Identity_Versioned/Bootstrap4/IdentityHostingStartup.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.AccessDenied.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.AccessDenied.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ConfirmEmail.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ConfirmEmail.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ConfirmEmailChange.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ConfirmEmailChange.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ExternalLogin.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ExternalLogin.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ForgotPassword.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ForgotPassword.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ForgotPasswordConfirmation.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ForgotPasswordConfirmation.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Lockout.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Lockout.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Login.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Login.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.LoginWith2fa.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.LoginWith2fa.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.LoginWithRecoveryCode.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.LoginWithRecoveryCode.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Logout.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Logout.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Register.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.Register.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.RegisterConfirmation.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.RegisterConfirmation.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ResendEmailConfirmation.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ResendEmailConfirmation.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ResetPassword.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ResetPassword.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ResetPasswordConfirmation.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account.ResetPasswordConfirmation.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account._StatusMessage.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Account._ViewImports.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ChangePassword.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ChangePassword.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.DeletePersonalData.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.DeletePersonalData.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.Disable2fa.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.Disable2fa.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.DownloadPersonalData.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.DownloadPersonalData.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.Email.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.Email.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.EnableAuthenticator.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.EnableAuthenticator.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ExternalLogins.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ExternalLogins.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.GenerateRecoveryCodes.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.GenerateRecoveryCodes.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.Index.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.Index.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ManageNavPages.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.PersonalData.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.PersonalData.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ResetAuthenticator.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ResetAuthenticator.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.SetPassword.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.SetPassword.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ShowRecoveryCodes.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.ShowRecoveryCodes.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.TwoFactorAuthentication.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage.TwoFactorAuthentication.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage._Layout.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage._ManageNav.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage._StatusMessage.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Account/Manage/Account.Manage._ViewImports.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Error.cs.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/Error.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/_Layout.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/_ValidationScriptsPartial.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/_ViewImports.cshtml", "Templates/Identity_Versioned/Bootstrap4/Pages/_ViewStart.cshtml", "Templates/Identity_Versioned/Bootstrap4/ScaffoldingReadme.cshtml", "Templates/Identity_Versioned/Bootstrap4/SupportPages._CookieConsentPartial.cshtml", "Templates/Identity_Versioned/Bootstrap4/SupportPages._ViewImports.cshtml", "Templates/Identity_Versioned/Bootstrap4/SupportPages._ViewStart.cshtml", "Templates/Identity_Versioned/Bootstrap4/_LoginPartial.cshtml", "Templates/Identity_Versioned/Bootstrap4/wwwroot/css/site.css", "Templates/Identity_Versioned/Bootstrap4/wwwroot/favicon.ico", "Templates/Identity_Versioned/Bootstrap4/wwwroot/js/site.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/LICENSE", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation/LICENSE.md", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation/dist/additional-methods.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery/LICENSE.txt", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery/dist/jquery.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery/dist/jquery.min.js", "Templates/Identity_Versioned/Bootstrap4/wwwroot/lib/jquery/dist/jquery.min.map", "Templates/MinimalApi/MinimalApi.cshtml", "Templates/MinimalApi/MinimalApiEf.cshtml", "Templates/MinimalApi/MinimalApiEfNoClass.cshtml", "Templates/MinimalApi/MinimalApiNoClass.cshtml", "Templates/MvcLayout/Error.cshtml", "Templates/MvcLayout/_Layout.cshtml", "Templates/RazorPageGenerator/Create.cshtml", "Templates/RazorPageGenerator/CreatePageModel.cshtml", "Templates/RazorPageGenerator/Delete.cshtml", "Templates/RazorPageGenerator/DeletePageModel.cshtml", "Templates/RazorPageGenerator/Details.cshtml", "Templates/RazorPageGenerator/DetailsPageModel.cshtml", "Templates/RazorPageGenerator/Edit.cshtml", "Templates/RazorPageGenerator/EditPageModel.cshtml", "Templates/RazorPageGenerator/List.cshtml", "Templates/RazorPageGenerator/ListPageModel.cshtml", "Templates/RazorPageGenerator/_ValidationScriptsPartial.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/Create.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/CreatePageModel.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/Delete.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/DeletePageModel.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/Details.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/DetailsPageModel.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/Edit.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/EditPageModel.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/List.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/ListPageModel.cshtml", "Templates/RazorPageGenerator_Versioned/Bootstrap4/_ValidationScriptsPartial.cshtml", "Templates/Startup/ReadMe.cshtml", "Templates/Startup/Startup.cshtml", "Templates/ViewGenerator/Create.cshtml", "Templates/ViewGenerator/Delete.cshtml", "Templates/ViewGenerator/Details.cshtml", "Templates/ViewGenerator/Edit.cshtml", "Templates/ViewGenerator/Empty.cshtml", "Templates/ViewGenerator/List.cshtml", "Templates/ViewGenerator/_ValidationScriptsPartial.cshtml", "Templates/ViewGenerator_Versioned/Bootstrap4/Create.cshtml", "Templates/ViewGenerator_Versioned/Bootstrap4/Delete.cshtml", "Templates/ViewGenerator_Versioned/Bootstrap4/Details.cshtml", "Templates/ViewGenerator_Versioned/Bootstrap4/Edit.cshtml", "Templates/ViewGenerator_Versioned/Bootstrap4/Empty.cshtml", "Templates/ViewGenerator_Versioned/Bootstrap4/List.cshtml", "Templates/ViewGenerator_Versioned/Bootstrap4/_ValidationScriptsPartial.cshtml", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll", "lib/net8.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.xml", "lib/net8.0/blazorIdentityChanges.json", "lib/net8.0/blazorWebCrudChanges.json", "lib/net8.0/bootstrap4_identitygeneratorfilesconfig.json", "lib/net8.0/bootstrap5_identitygeneratorfilesconfig.json", "lib/net8.0/identityMinimalHostingChanges.json", "lib/net8.0/minimalApiChanges.json", "microsoft.visualstudio.web.codegenerators.mvc.8.0.7.nupkg.sha512", "microsoft.visualstudio.web.codegenerators.mvc.nuspec"]}, "Microsoft.Win32.SystemEvents/7.0.0": {"sha512": "2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "type": "package", "path": "microsoft.win32.systemevents/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.7.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Mono.TextTemplating/2.3.1": {"sha512": "pqYwzNqDL0QK1JFpAjpI/NPqyqLGpHLvVmA5Ec0LaSnbIDtEXxu0td16uunegb7c8xAnlcm4qkbIYUP5FfrFpA==", "type": "package", "path": "mono.texttemplating/2.3.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/Mono.TextTemplating.dll", "lib/netcoreapp2.1/Mono.TextTemplating.dll", "lib/netcoreapp3.1/Mono.TextTemplating.dll", "lib/netstandard2.0/Mono.TextTemplating.dll", "mono.texttemplating.2.3.1.nupkg.sha512", "mono.texttemplating.nuspec", "readme.md"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NuGet.Common/6.11.0": {"sha512": "T3bCiKUSx8wdYpcqr6Dbx93zAqFp689ee/oa1tH22XI/xl7EUzQ7No/WlE1FUqvEX1+Mqar3wRNAn2O/yxo94g==", "type": "package", "path": "nuget.common/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.Common.dll", "lib/netstandard2.0/NuGet.Common.dll", "nuget.common.6.11.0.nupkg.sha512", "nuget.common.nuspec"]}, "NuGet.Configuration/6.11.0": {"sha512": "73QprQqmumFrv3Ooi4YWpRYeBj8jZy9gNdOaOCp4pPInpt41SJJAz/aP4je+StwIJvi5HsgPPecLKekDIQEwKg==", "type": "package", "path": "nuget.configuration/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.Configuration.dll", "lib/netstandard2.0/NuGet.Configuration.dll", "nuget.configuration.6.11.0.nupkg.sha512", "nuget.configuration.nuspec"]}, "NuGet.DependencyResolver.Core/6.11.0": {"sha512": "SoiPKPooA+IF+iCsX1ykwi3M0e+yBL34QnwIP3ujhQEn1dhlP/N1XsYAnKkJPxV15EZCahuuS4HtnBsZx+CHKA==", "type": "package", "path": "nuget.dependencyresolver.core/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.DependencyResolver.Core.dll", "lib/net5.0/NuGet.DependencyResolver.Core.dll", "lib/netstandard2.0/NuGet.DependencyResolver.Core.dll", "nuget.dependencyresolver.core.6.11.0.nupkg.sha512", "nuget.dependencyresolver.core.nuspec"]}, "NuGet.Frameworks/6.11.0": {"sha512": "Ew/mrfmLF5phsprysHbph2+tdZ10HMHAURavsr/Kx1WhybDG4vmGuoNLbbZMZOqnPRdpyCTc42OKWLoedxpYtA==", "type": "package", "path": "nuget.frameworks/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.Frameworks.dll", "lib/netstandard2.0/NuGet.Frameworks.dll", "nuget.frameworks.6.11.0.nupkg.sha512", "nuget.frameworks.nuspec"]}, "NuGet.LibraryModel/6.11.0": {"sha512": "KUV2eeMICMb24OPcICn/wgncNzt6+W+lmFVO5eorTdo1qV4WXxYGyG1NTPiCY+Nrv5H/Ilnv9UaUM2ozqSmnjw==", "type": "package", "path": "nuget.librarymodel/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.LibraryModel.dll", "lib/netstandard2.0/NuGet.LibraryModel.dll", "nuget.librarymodel.6.11.0.nupkg.sha512", "nuget.librarymodel.nuspec"]}, "NuGet.Packaging/6.11.0": {"sha512": "VmUv2LedVuPY1tfNybORO2I9IuqOzeV7I5JBD+PwNvJq2bAqovi4FCw2cYI0g+kjOJXBN2lAJfrfnqtUOlVJdQ==", "type": "package", "path": "nuget.packaging/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.Packaging.dll", "lib/net5.0/NuGet.Packaging.dll", "lib/netstandard2.0/NuGet.Packaging.dll", "nuget.packaging.6.11.0.nupkg.sha512", "nuget.packaging.nuspec"]}, "NuGet.ProjectModel/6.11.0": {"sha512": "g0KtmDH6fas97WsN73yV2h1F5JT9o6+Y0wlPK+ij9YLKaAXaF6+1HkSaQMMJ+xh9/jCJG9G6nau6InOlb1g48g==", "type": "package", "path": "nuget.projectmodel/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.ProjectModel.dll", "lib/net5.0/NuGet.ProjectModel.dll", "lib/netstandard2.0/NuGet.ProjectModel.dll", "nuget.projectmodel.6.11.0.nupkg.sha512", "nuget.projectmodel.nuspec"]}, "NuGet.Protocol/6.11.0": {"sha512": "p5B8oNLLnGhUfMbcS16aRiegj11pD6k+LELyRBqvNFR/pE3yR1XT+g1XS33ME9wvoU+xbCGnl4Grztt1jHPinw==", "type": "package", "path": "nuget.protocol/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.Protocol.dll", "lib/net5.0/NuGet.Protocol.dll", "lib/netstandard2.0/NuGet.Protocol.dll", "nuget.protocol.6.11.0.nupkg.sha512", "nuget.protocol.nuspec"]}, "NuGet.Versioning/6.11.0": {"sha512": "v/GGlIj2dd7svplFmASWEueu62veKW0MrMtBaZ7QG8aJTSGv2yE+pgUGhXRcQ4nxNOEq/wLBrz1vkth/1SND7A==", "type": "package", "path": "nuget.versioning/6.11.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.Versioning.dll", "lib/netstandard2.0/NuGet.Versioning.dll", "nuget.versioning.6.11.0.nupkg.sha512", "nuget.versioning.nuspec"]}, "System.CodeDom/5.0.0": {"sha512": "JPJArwA1kdj8qDAkY2XGjSWoYnqiM7q/3yRNkt6n28Mnn95MuEGkZXUbPBf7qc3IjwrGY5ttQon7yqHZyQJmOQ==", "type": "package", "path": "system.codedom/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/net461/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.5.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/7.0.0": {"sha512": "dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "type": "package", "path": "system.collections.immutable/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.7.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition/7.0.0": {"sha512": "tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "type": "package", "path": "system.composition/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.targets", "lib/net461/_._", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/_._", "system.composition.7.0.0.nupkg.sha512", "system.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.AttributedModel/7.0.0": {"sha512": "2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "type": "package", "path": "system.composition.attributedmodel/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.AttributedModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets", "lib/net462/System.Composition.AttributedModel.dll", "lib/net462/System.Composition.AttributedModel.xml", "lib/net6.0/System.Composition.AttributedModel.dll", "lib/net6.0/System.Composition.AttributedModel.xml", "lib/net7.0/System.Composition.AttributedModel.dll", "lib/net7.0/System.Composition.AttributedModel.xml", "lib/netstandard2.0/System.Composition.AttributedModel.dll", "lib/netstandard2.0/System.Composition.AttributedModel.xml", "system.composition.attributedmodel.7.0.0.nupkg.sha512", "system.composition.attributedmodel.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Convention/7.0.0": {"sha512": "IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "type": "package", "path": "system.composition.convention/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Convention.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets", "lib/net462/System.Composition.Convention.dll", "lib/net462/System.Composition.Convention.xml", "lib/net6.0/System.Composition.Convention.dll", "lib/net6.0/System.Composition.Convention.xml", "lib/net7.0/System.Composition.Convention.dll", "lib/net7.0/System.Composition.Convention.xml", "lib/netstandard2.0/System.Composition.Convention.dll", "lib/netstandard2.0/System.Composition.Convention.xml", "system.composition.convention.7.0.0.nupkg.sha512", "system.composition.convention.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Hosting/7.0.0": {"sha512": "eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "type": "package", "path": "system.composition.hosting/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets", "lib/net462/System.Composition.Hosting.dll", "lib/net462/System.Composition.Hosting.xml", "lib/net6.0/System.Composition.Hosting.dll", "lib/net6.0/System.Composition.Hosting.xml", "lib/net7.0/System.Composition.Hosting.dll", "lib/net7.0/System.Composition.Hosting.xml", "lib/netstandard2.0/System.Composition.Hosting.dll", "lib/netstandard2.0/System.Composition.Hosting.xml", "system.composition.hosting.7.0.0.nupkg.sha512", "system.composition.hosting.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Runtime/7.0.0": {"sha512": "aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "type": "package", "path": "system.composition.runtime/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Runtime.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets", "lib/net462/System.Composition.Runtime.dll", "lib/net462/System.Composition.Runtime.xml", "lib/net6.0/System.Composition.Runtime.dll", "lib/net6.0/System.Composition.Runtime.xml", "lib/net7.0/System.Composition.Runtime.dll", "lib/net7.0/System.Composition.Runtime.xml", "lib/netstandard2.0/System.Composition.Runtime.dll", "lib/netstandard2.0/System.Composition.Runtime.xml", "system.composition.runtime.7.0.0.nupkg.sha512", "system.composition.runtime.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.TypedParts/7.0.0": {"sha512": "ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "type": "package", "path": "system.composition.typedparts/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.TypedParts.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets", "lib/net462/System.Composition.TypedParts.dll", "lib/net462/System.Composition.TypedParts.xml", "lib/net6.0/System.Composition.TypedParts.dll", "lib/net6.0/System.Composition.TypedParts.xml", "lib/net7.0/System.Composition.TypedParts.dll", "lib/net7.0/System.Composition.TypedParts.xml", "lib/netstandard2.0/System.Composition.TypedParts.dll", "lib/netstandard2.0/System.Composition.TypedParts.xml", "system.composition.typedparts.7.0.0.nupkg.sha512", "system.composition.typedparts.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/7.0.0": {"sha512": "WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "type": "package", "path": "system.configuration.configurationmanager/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.7.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.DataSetExtensions/4.5.0": {"sha512": "221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "type": "package", "path": "system.data.datasetextensions/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netstandard2.0/System.Data.DataSetExtensions.dll", "ref/net45/_._", "ref/netstandard2.0/System.Data.DataSetExtensions.dll", "system.data.datasetextensions.4.5.0.nupkg.sha512", "system.data.datasetextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/7.0.0": {"sha512": "eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "type": "package", "path": "system.diagnostics.eventlog/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.7.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/7.0.0": {"sha512": "KIX+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "type": "package", "path": "system.drawing.common/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/net7.0/System.Drawing.Common.dll", "runtimes/win/lib/net7.0/System.Drawing.Common.xml", "system.drawing.common.7.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/8.0.1": {"sha512": "XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "type": "package", "path": "system.formats.asn1/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/net7.0/System.Formats.Asn1.dll", "lib/net7.0/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.8.0.1.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"sha512": "yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO.FileSystem.AccessControl/5.0.0": {"sha512": "SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "type": "package", "path": "system.io.filesystem.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.xml", "lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "ref/net46/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/de/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/es/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/it/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.AccessControl.xml", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/net46/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "system.io.filesystem.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Pipelines/7.0.0": {"sha512": "jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "type": "package", "path": "system.io.pipelines/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.7.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/7.0.0": {"sha512": "MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "type": "package", "path": "system.reflection.metadata/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Reflection.Metadata.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.Metadata.targets", "lib/net462/System.Reflection.Metadata.dll", "lib/net462/System.Reflection.Metadata.xml", "lib/net6.0/System.Reflection.Metadata.dll", "lib/net6.0/System.Reflection.Metadata.xml", "lib/net7.0/System.Reflection.Metadata.dll", "lib/net7.0/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "system.reflection.metadata.7.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt"]}, "System.Reflection.MetadataLoadContext/7.0.0": {"sha512": "z9PvtMJra5hK8n+g0wmPtaG7HQRZpTmIPRw5Z0LEemlcdQMHuTD5D7OAY/fZuuz1L9db++QOcDF0gJTLpbMtZQ==", "type": "package", "path": "system.reflection.metadataloadcontext/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Reflection.MetadataLoadContext.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.MetadataLoadContext.targets", "lib/net462/System.Reflection.MetadataLoadContext.dll", "lib/net462/System.Reflection.MetadataLoadContext.xml", "lib/net6.0/System.Reflection.MetadataLoadContext.dll", "lib/net6.0/System.Reflection.MetadataLoadContext.xml", "lib/net7.0/System.Reflection.MetadataLoadContext.dll", "lib/net7.0/System.Reflection.MetadataLoadContext.xml", "lib/netstandard2.0/System.Reflection.MetadataLoadContext.dll", "lib/netstandard2.0/System.Reflection.MetadataLoadContext.xml", "system.reflection.metadataloadcontext.7.0.0.nupkg.sha512", "system.reflection.metadataloadcontext.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/6.0.0": {"sha512": "E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "type": "package", "path": "system.runtime.caching/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/netcoreapp3.1/System.Runtime.Caching.dll", "lib/netcoreapp3.1/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.dll", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.xml", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.xml", "system.runtime.caching.6.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Cng/5.0.0": {"sha512": "jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "type": "package", "path": "system.security.cryptography.cng/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.xml", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.xml", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.xml", "lib/netstandard2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard2.1/System.Security.Cryptography.Cng.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/netstandard2.1/System.Security.Cryptography.Cng.dll", "ref/netstandard2.1/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.5.0.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/6.0.4": {"sha512": "LGbXi1oUJ9QgCNGXRO9ndzBL/GZgANcsURpMhNR8uO+rca47SZmciS3RSQUvlQRwK3QHZSHNOXzoMUASKA+Anw==", "type": "package", "path": "system.security.cryptography.pkcs/6.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Pkcs.dll", "lib/net461/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.6.0.4.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/7.0.0": {"sha512": "xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "type": "package", "path": "system.security.cryptography.protecteddata/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/7.0.0": {"sha512": "Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "type": "package", "path": "system.security.permissions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Permissions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "lib/net462/System.Security.Permissions.dll", "lib/net462/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/net7.0/System.Security.Permissions.dll", "lib/net7.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.7.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.0": {"sha512": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "type": "package", "path": "system.text.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/7.0.0": {"sha512": "qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "type": "package", "path": "system.threading.channels/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.Channels.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "lib/net462/System.Threading.Channels.dll", "lib/net462/System.Threading.Channels.xml", "lib/net6.0/System.Threading.Channels.dll", "lib/net6.0/System.Threading.Channels.xml", "lib/net7.0/System.Threading.Channels.dll", "lib/net7.0/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.7.0.0.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Dataflow/7.0.0": {"sha512": "BmSJ4b0e2nlplV/RdWVxvH7WECTHACofv06dx/JwOYc0n56eK1jIWdQKNYYsReSO4w8n1QA5stOzSQcfaVBkJg==", "type": "package", "path": "system.threading.tasks.dataflow/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.Tasks.Dataflow.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.Tasks.Dataflow.targets", "lib/net462/System.Threading.Tasks.Dataflow.dll", "lib/net462/System.Threading.Tasks.Dataflow.xml", "lib/net6.0/System.Threading.Tasks.Dataflow.dll", "lib/net6.0/System.Threading.Tasks.Dataflow.xml", "lib/net7.0/System.Threading.Tasks.Dataflow.dll", "lib/net7.0/System.Threading.Tasks.Dataflow.xml", "lib/netstandard2.0/System.Threading.Tasks.Dataflow.dll", "lib/netstandard2.0/System.Threading.Tasks.Dataflow.xml", "lib/netstandard2.1/System.Threading.Tasks.Dataflow.dll", "lib/netstandard2.1/System.Threading.Tasks.Dataflow.xml", "system.threading.tasks.dataflow.7.0.0.nupkg.sha512", "system.threading.tasks.dataflow.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Windows.Extensions/7.0.0": {"sha512": "bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "type": "package", "path": "system.windows.extensions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/net7.0/System.Windows.Extensions.dll", "lib/net7.0/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/net7.0/System.Windows.Extensions.dll", "runtimes/win/lib/net7.0/System.Windows.Extensions.xml", "system.windows.extensions.7.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "DrugPrevention.Repositories.NamND/1.0.0": {"type": "project", "path": "../DrugPrevention.Repositories.NamND/DrugPrevention.Repositories.NamND.csproj", "msbuildProject": "../DrugPrevention.Repositories.NamND/DrugPrevention.Repositories.NamND.csproj"}, "DrugPrevention.Services.NamND/1.0.0": {"type": "project", "path": "../DrugPrevention.Services.NamND/DrugPrevention.Services.NamND.csproj", "msbuildProject": "../DrugPrevention.Services.NamND/DrugPrevention.Services.NamND.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["DrugPrevention.Services.NamND >= 1.0.0", "Microsoft.EntityFrameworkCore.Tools >= 8.0.11", "Microsoft.VisualStudio.Web.CodeGeneration.Design >= 8.0.7"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\DrugPrevention.MVCWebApp.NamND.csproj", "projectName": "DrugPrevention.MVCWebApp.NamND", "projectPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\DrugPrevention.MVCWebApp.NamND.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\DrugPrevention.Services.NamND.csproj": {"projectPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\DrugPrevention.Services.NamND.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[8.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}