@model IEnumerable<DrugPrevention.Repositories.NamND.Models.SurveyQuestionsNamND>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.QuestionText)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.QuestionOrder)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.IsActive)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CreatedDate)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.QuestionType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.IsRequired)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Options)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.RiskWeight)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.HelpText)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Section)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Tag)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Language)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DependsOnQuestionID)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DependsOnAnswer)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.MinScore)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.MaxScore)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DisplayStyle)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ImageURL)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.SurveyNamND)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.QuestionText)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.QuestionOrder)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.IsActive)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CreatedDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.QuestionType)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.IsRequired)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Options)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.RiskWeight)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.HelpText)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Section)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Tag)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Language)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.DependsOnQuestionID)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.DependsOnAnswer)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MinScore)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MaxScore)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.DisplayStyle)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.ImageURL)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SurveyNamND.SurveyName)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.QuestionNamNDID">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.QuestionNamNDID">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.QuestionNamNDID">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
