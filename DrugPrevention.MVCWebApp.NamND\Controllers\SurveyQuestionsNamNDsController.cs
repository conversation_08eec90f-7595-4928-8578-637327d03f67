﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using DrugPrevention.Repositories.NamND.Models;
using DrugPrevention.Services.NamND;

namespace DrugPrevention.MVCWebApp.NamND.Controllers
{
    public class SurveyQuestionsNamNDsController : Controller
    {
        private readonly IServiceProviders _serviceProviders;
        //private readonly SU25_PRN222_SE1709_G2_DrugPreventionSystemContext _context;

        public SurveyQuestionsNamNDsController(IServiceProviders serviceProviders) =>
            _serviceProviders = serviceProviders;
        

        //public SurveyQuestionsNamNDsController(SU25_PRN222_SE1709_G2_DrugPreventionSystemContext context)
        //{
        //    _context = context;
        //}

        // GET: SurveyQuestionsNamNDs
        public async Task<IActionResult> Index()
        {
            var surveyQuestions = await _serviceProviders.SurveyQuestionsNamNDService.GetAllAsync();
            return View(surveyQuestions);
        }

        // GET: SurveyQuestionsNamNDs/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var surveyQuestionsNamND = await _serviceProviders.SurveyQuestionsNamNDService.GetByIdAsync(id.Value); 

            if (surveyQuestionsNamND == null)
            {
                return NotFound();
            }

            return View(surveyQuestionsNamND);
        }

        // GET: SurveyQuestionsNamNDs/Create
        //        public IActionResult Create()
        //        {
        //            ViewData["SurveyNamNDID"] = new SelectList(_context.SurveysNamNDs, "SurveyNamNDID", "SurveyName");
        //            return View();
        //        }

        //        // POST: SurveyQuestionsNamNDs/Create
        //        // To protect from overposting attacks, enable the specific properties you want to bind to.
        //        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        //        [HttpPost]
        //        [ValidateAntiForgeryToken]
        //        public async Task<IActionResult> Create([Bind("QuestionNamNDID,SurveyNamNDID,QuestionText,QuestionOrder,IsActive,CreatedDate,QuestionType,IsRequired,Options,RiskWeight,HelpText,Section,Tag,Language,DependsOnQuestionID,DependsOnAnswer,MinScore,MaxScore,DisplayStyle,ImageURL")] SurveyQuestionsNamND surveyQuestionsNamND)
        //        {
        //            if (ModelState.IsValid)
        //            {
        //                _context.Add(surveyQuestionsNamND);
        //                await _context.SaveChangesAsync();
        //                return RedirectToAction(nameof(Index));
        //            }
        //            ViewData["SurveyNamNDID"] = new SelectList(_context.SurveysNamNDs, "SurveyNamNDID", "SurveyName", surveyQuestionsNamND.SurveyNamNDID);
        //            return View(surveyQuestionsNamND);
        //        }

        //        // GET: SurveyQuestionsNamNDs/Edit/5
        //        public async Task<IActionResult> Edit(int? id)
        //        {
        //            if (id == null)
        //            {
        //                return NotFound();
        //            }

        //            var surveyQuestionsNamND = await _context.SurveyQuestionsNamNDs.FindAsync(id);
        //            if (surveyQuestionsNamND == null)
        //            {
        //                return NotFound();
        //            }
        //            ViewData["SurveyNamNDID"] = new SelectList(_context.SurveysNamNDs, "SurveyNamNDID", "SurveyName", surveyQuestionsNamND.SurveyNamNDID);
        //            return View(surveyQuestionsNamND);
        //        }

        //        // POST: SurveyQuestionsNamNDs/Edit/5
        //        // To protect from overposting attacks, enable the specific properties you want to bind to.
        //        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        //        [HttpPost]
        //        [ValidateAntiForgeryToken]
        //        public async Task<IActionResult> Edit(int id, [Bind("QuestionNamNDID,SurveyNamNDID,QuestionText,QuestionOrder,IsActive,CreatedDate,QuestionType,IsRequired,Options,RiskWeight,HelpText,Section,Tag,Language,DependsOnQuestionID,DependsOnAnswer,MinScore,MaxScore,DisplayStyle,ImageURL")] SurveyQuestionsNamND surveyQuestionsNamND)
        //        {
        //            if (id != surveyQuestionsNamND.QuestionNamNDID)
        //            {
        //                return NotFound();
        //            }

        //            if (ModelState.IsValid)
        //            {
        //                try
        //                {
        //                    _context.Update(surveyQuestionsNamND);
        //                    await _context.SaveChangesAsync();
        //                }
        //                catch (DbUpdateConcurrencyException)
        //                {
        //                    if (!SurveyQuestionsNamNDExists(surveyQuestionsNamND.QuestionNamNDID))
        //                    {
        //                        return NotFound();
        //                    }
        //                    else
        //                    {
        //                        throw;
        //                    }
        //                }
        //                return RedirectToAction(nameof(Index));
        //            }
        //            ViewData["SurveyNamNDID"] = new SelectList(_context.SurveysNamNDs, "SurveyNamNDID", "SurveyName", surveyQuestionsNamND.SurveyNamNDID);
        //            return View(surveyQuestionsNamND);
        //        }

        //        // GET: SurveyQuestionsNamNDs/Delete/5
        //        public async Task<IActionResult> Delete(int? id)
        //        {
        //            if (id == null)
        //            {
        //                return NotFound();
        //            }

        //            var surveyQuestionsNamND = await _context.SurveyQuestionsNamNDs
        //                .Include(s => s.SurveyNamND)
        //                .FirstOrDefaultAsync(m => m.QuestionNamNDID == id);
        //            if (surveyQuestionsNamND == null)
        //            {
        //                return NotFound();
        //            }

        //            return View(surveyQuestionsNamND);
        //        }

        //        // POST: SurveyQuestionsNamNDs/Delete/5
        //        [HttpPost, ActionName("Delete")]
        //        [ValidateAntiForgeryToken]
        //        public async Task<IActionResult> DeleteConfirmed(int id)
        //        {
        //            var surveyQuestionsNamND = await _context.SurveyQuestionsNamNDs.FindAsync(id);
        //            if (surveyQuestionsNamND != null)
        //            {
        //                _context.SurveyQuestionsNamNDs.Remove(surveyQuestionsNamND);
        //            }

        //            await _context.SaveChangesAsync();
        //            return RedirectToAction(nameof(Index));
        //        }

        //        private bool SurveyQuestionsNamNDExists(int id)
        //        {
        //            return _context.SurveyQuestionsNamNDs.Any(e => e.QuestionNamNDID == id);
        //        }
    }
}
