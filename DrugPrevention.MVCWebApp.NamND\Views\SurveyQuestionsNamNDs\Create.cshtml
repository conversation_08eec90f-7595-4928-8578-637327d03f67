﻿@model DrugPrevention.Repositories.NamND.Models.SurveyQuestionsNamND

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<h4>SurveyQuestionsNamND</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="SurveyNamNDID" class="control-label"></label>
                <select asp-for="SurveyNamNDID" class ="form-control" asp-items="ViewBag.SurveyNamNDID"></select>
            </div>
            <div class="form-group">
                <label asp-for="QuestionText" class="control-label"></label>
                <input asp-for="QuestionText" class="form-control" />
                <span asp-validation-for="QuestionText" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="QuestionOrder" class="control-label"></label>
                <input asp-for="QuestionOrder" class="form-control" />
                <span asp-validation-for="QuestionOrder" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CreatedDate" class="control-label"></label>
                <input asp-for="CreatedDate" class="form-control" />
                <span asp-validation-for="CreatedDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="QuestionType" class="control-label"></label>
                <input asp-for="QuestionType" class="form-control" />
                <span asp-validation-for="QuestionType" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Options" class="control-label"></label>
                <input asp-for="Options" class="form-control" />
                <span asp-validation-for="Options" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="HelpText" class="control-label"></label>
                <input asp-for="HelpText" class="form-control" />
                <span asp-validation-for="HelpText" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Section" class="control-label"></label>
                <input asp-for="Section" class="form-control" />
                <span asp-validation-for="Section" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Tag" class="control-label"></label>
                <input asp-for="Tag" class="form-control" />
                <span asp-validation-for="Tag" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DependsOnQuestionID" class="control-label"></label>
                <input asp-for="DependsOnQuestionID" class="form-control" />
                <span asp-validation-for="DependsOnQuestionID" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DependsOnAnswer" class="control-label"></label>
                <input asp-for="DependsOnAnswer" class="form-control" />
                <span asp-validation-for="DependsOnAnswer" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="MinScore" class="control-label"></label>
                <input asp-for="MinScore" class="form-control" />
                <span asp-validation-for="MinScore" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="MaxScore" class="control-label"></label>
                <input asp-for="MaxScore" class="form-control" />
                <span asp-validation-for="MaxScore" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DisplayStyle" class="control-label"></label>
                <input asp-for="DisplayStyle" class="form-control" />
                <span asp-validation-for="DisplayStyle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ImageURL" class="control-label"></label>
                <input asp-for="ImageURL" class="form-control" />
                <span asp-validation-for="ImageURL" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
