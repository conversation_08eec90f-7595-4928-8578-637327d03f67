﻿@model DrugPrevention.Repositories.NamND.Models.SurveyQuestionsNamND

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>SurveyQuestionsNamND</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.QuestionText)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.QuestionText)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.QuestionOrder)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.QuestionOrder)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.IsActive)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.IsActive)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CreatedDate)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CreatedDate)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.QuestionType)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.QuestionType)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.IsRequired)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.IsRequired)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Options)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Options)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.RiskWeight)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.RiskWeight)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.HelpText)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.HelpText)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Section)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Section)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Tag)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Tag)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Language)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Language)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.DependsOnQuestionID)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.DependsOnQuestionID)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.DependsOnAnswer)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.DependsOnAnswer)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.MinScore)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.MinScore)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.MaxScore)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.MaxScore)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.DisplayStyle)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.DisplayStyle)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ImageURL)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ImageURL)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.SurveyNamND)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.SurveyNamND.SurveyName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.QuestionNamNDID">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
