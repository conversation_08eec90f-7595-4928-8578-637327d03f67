/* _content/DrugPrevention.MVCWebApp.NamND/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-ir166smycq] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-ir166smycq] {
  color: #0077cc;
}

.btn-primary[b-ir166smycq] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-ir166smycq], .nav-pills .show > .nav-link[b-ir166smycq] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-ir166smycq] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-ir166smycq] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-ir166smycq] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-ir166smycq] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-ir166smycq] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
