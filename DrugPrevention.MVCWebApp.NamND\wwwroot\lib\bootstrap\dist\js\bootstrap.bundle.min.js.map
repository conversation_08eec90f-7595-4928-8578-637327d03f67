{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/base-component.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "j<PERSON>y", "nodeType", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getComputedStyle", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "parentNode", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "index", "indexOf", "listLength", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "i", "len", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFn", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFn", "replace", "dom<PERSON><PERSON>s", "querySelectorAll", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "[object Object]", "getInstance", "VERSION", "Error", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "closest", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "pageYOffset", "left", "pageXOffset", "position", "offsetTop", "offsetLeft", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "touches", "clientX", "move", "end", "clearTimeout", "itemImg", "e", "add", "_getItemByOrder", "isNext", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "triggerSlidEvent", "completeCallBack", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activesData", "actives", "container", "tempActiveData", "elemActive", "dimension", "_getDimension", "style", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selected", "trigger<PERSON><PERSON>y", "isOpen", "bottom", "right", "basePlacements", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "styles", "assign", "effect", "_ref2", "initialStyles", "popper", "options", "strategy", "margin", "arrow", "reference", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "round", "includeScale", "scaleX", "scaleY", "width", "offsetWidth", "height", "x", "y", "getLayoutRect", "clientRect", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "userAgent", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "_ref3", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "_ref3$x", "_ref3$y", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "computeStyles$1", "_ref4", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "data-popper-placement", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "scrollTop", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "listScrollParents", "_element$ownerDocumen", "getScrollParent", "isBody", "visualViewport", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "html", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "getVariation", "computeOffsets", "variation", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "referenceElement", "clippingClientRect", "mainClippingParents", "clippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "data-popper-reference-hidden", "data-popper-escaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_mainSide", "_altSide", "_offset", "_min", "_max", "_preventedOffset", "getCompositeRect", "elementOrVirtualElement", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "isElementScaled", "DEFAULT_OPTIONS", "modifiers", "areValidElements", "_len", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "cleanupModifierEffects", "merged", "visited", "result", "modifier", "dep", "depModifier", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "REGEXP_KEYDOWN", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "getParentFromElement", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "_getPopperConfig", "isDisplayStatic", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "items", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isActive", "stopPropagation", "getToggleButton", "clearMenus", "dataApiKeydownHandler", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "styleProp", "scrollbarWidth", "_applyManipulationCallback", "_resetElementAttributes", "actualValue", "removeProperty", "callBack", "isOverflowing", "className", "rootElement", "clickCallback", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "trapElement", "autofocus", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_ignoreBackdropClick", "_scrollBar", "_isAnimated", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_triggerBackdropTransition", "_resetAdjustments", "currentTarget", "isModalOverflowing", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "<PERSON><PERSON><PERSON>", "visibility", "blur", "allReadyOpen", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "*", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "attachment", "_getAttachment", "_addAttachmentClass", "_resolvePossibleFunction", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON>", "_sanitizeAndSetContent", "content", "templateElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "updateAttachment", "_getDelegateConfig", "_handlePopperPlacementChange", "_getBasicClassPrefix", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "basicClassPrefixRegex", "tabClass", "token", "tClass", "Popover", "_getContent", "SELECTOR_LINK_ITEMS", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "listGroup", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "complete", "active", "isTransitioning", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;0OAOA,MA2BMA,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAWH,EAAQE,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAY,IAAGA,EAASG,MAAM,KAAK,IAGrCL,EAAWE,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAON,GAGHO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKQ,SAASC,cAAcT,GAAYA,EAGrC,MAGHU,EAAyBX,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWQ,SAASC,cAAcT,GAAY,MA0BjDW,EAAuBZ,IAC3BA,EAAQa,cAAc,IAAIC,MA1FL,mBA6FjBC,EAAYC,MACXA,GAAsB,iBAARA,UAIO,IAAfA,EAAIC,SACbD,EAAMA,EAAI,SAGmB,IAAjBA,EAAIE,UAGdC,EAAaH,GACbD,EAAUC,GACLA,EAAIC,OAASD,EAAI,GAAKA,EAGZ,iBAARA,GAAoBA,EAAII,OAAS,EACnCX,SAASC,cAAcM,GAGzB,KAGHK,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASf,EAAUe,GAAS,UArH5Cd,OADSA,EAsHsDc,GApHzD,GAAEd,EAGL,GAAGgB,SAASC,KAAKjB,GAAKkB,MAAM,eAAe,GAAGC,cALxCnB,IAAAA,EAwHX,IAAK,IAAIoB,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,UACP,GAAEhB,EAAciB,0BAA0BX,qBAA4BG,yBAAiCF,UAM1GW,EAAYxC,MACXe,EAAUf,IAAgD,IAApCA,EAAQyC,iBAAiBrB,SAIgB,YAA7DsB,iBAAiB1C,GAAS2C,iBAAiB,cAG9CC,EAAa5C,IACZA,GAAWA,EAAQkB,WAAa2B,KAAKC,gBAItC9C,EAAQ+C,UAAUC,SAAS,mBAIC,IAArBhD,EAAQiD,SACVjD,EAAQiD,SAGVjD,EAAQkD,aAAa,aAAoD,UAArClD,EAAQE,aAAa,aAG5DiD,EAAiBnD,IACrB,IAAKS,SAAS2C,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrD,EAAQsD,YAA4B,CAC7C,MAAMC,EAAOvD,EAAQsD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIvD,aAAmBwD,WACdxD,EAIJA,EAAQyD,WAINN,EAAenD,EAAQyD,YAHrB,MAMLC,EAAO,OAUPC,EAAS3D,IAEbA,EAAQ4D,cAGJC,EAAY,KAChB,MAAMC,OAAEA,GAAWC,OAEnB,OAAID,IAAWrD,SAASuD,KAAKd,aAAa,qBACjCY,EAGF,MAGHG,EAA4B,GAiB5BC,EAAQ,IAAuC,QAAjCzD,SAAS2C,gBAAgBe,IAEvCC,EAAqBC,IAjBAC,IAAAA,EAAAA,EAkBN,KACjB,MAAMC,EAAIV,IAEV,GAAIU,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,mBA3BQ,YAAxBnE,SAASsE,YAENd,EAA0B7C,QAC7BX,SAASuE,iBAAiB,mBAAoB,KAC5Cf,EAA0BtC,QAAQ2C,GAAYA,OAIlDL,EAA0BgB,KAAKX,IAE/BA,KAuBEY,EAAUZ,IACU,mBAAbA,GACTA,KAIEa,EAAyB,CAACb,EAAUc,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAH,EAAQZ,GAIV,MACMgB,EA1LiCtF,CAAAA,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIuF,mBAAEA,EAAFC,gBAAsBA,GAAoBzB,OAAOrB,iBAAiB1C,GAEtE,MAAMyF,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBjF,MAAM,KAAK,GACnDkF,EAAkBA,EAAgBlF,MAAM,KAAK,GArFf,KAuFtBoF,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GA6KgBK,CAAiCT,GADlC,EAGxB,IAAIU,GAAS,EAEb,MAAMC,EAAU,EAAGC,OAAAA,MACbA,IAAWZ,IAIfU,GAAS,EACTV,EAAkBa,oBAtQC,gBAsQmCF,GACtDb,EAAQZ,KAGVc,EAAkBJ,iBA1QG,gBA0Q8Be,GACnDG,WAAW,KACJJ,GACHlF,EAAqBwE,IAEtBE,IAYCa,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,IAAIC,EAAQJ,EAAKK,QAAQJ,GAGzB,IAAe,IAAXG,EACF,OAAOJ,GAAME,GAAiBC,EAAiBH,EAAKhF,OAAS,EAAI,GAGnE,MAAMsF,EAAaN,EAAKhF,OAQxB,OANAoF,GAASF,EAAgB,GAAK,EAE1BC,IACFC,GAASA,EAAQE,GAAcA,GAG1BN,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIL,EAAOE,EAAa,MCrSjDI,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAoB,4BACpBC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAYzH,EAAS0H,GAC5B,OAAQA,GAAQ,GAAEA,MAAQR,OAAiBlH,EAAQkH,UAAYA,IAGjE,SAASS,EAAS3H,GAChB,MAAM0H,EAAMD,EAAYzH,GAKxB,OAHAA,EAAQkH,SAAWQ,EACnBT,EAAcS,GAAOT,EAAcS,IAAQ,GAEpCT,EAAcS,GAuCvB,SAASE,EAAYC,EAAQ9B,EAAS+B,EAAqB,MACzD,MAAMC,EAAetG,OAAOC,KAAKmG,GAEjC,IAAK,IAAIG,EAAI,EAAGC,EAAMF,EAAa3G,OAAQ4G,EAAIC,EAAKD,IAAK,CACvD,MAAME,EAAQL,EAAOE,EAAaC,IAElC,GAAIE,EAAMC,kBAAoBpC,GAAWmC,EAAMJ,qBAAuBA,EACpE,OAAOI,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBtC,EAASuC,GACnD,MAAMC,EAAgC,iBAAZxC,EACpBoC,EAAkBI,EAAaD,EAAevC,EAEpD,IAAIyC,EAAYC,EAAaJ,GAO7B,OANiBd,EAAamB,IAAIF,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAW3I,EAASqI,EAAmBtC,EAASuC,EAAcM,GACrE,GAAiC,iBAAtBP,IAAmCrI,EAC5C,OAUF,GAPK+F,IACHA,EAAUuC,EACVA,EAAe,MAKbhB,EAAkBjF,KAAKgG,GAAoB,CAC7C,MAAMQ,EAASlE,GACN,SAAUuD,GACf,IAAKA,EAAMY,eAAkBZ,EAAMY,gBAAkBZ,EAAMa,iBAAmBb,EAAMa,eAAe/F,SAASkF,EAAMY,eAChH,OAAOnE,EAAG1C,KAAK+G,KAAMd,IAKvBI,EACFA,EAAeO,EAAOP,GAEtBvC,EAAU8C,EAAO9C,GAIrB,MAAOwC,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBtC,EAASuC,GACvFT,EAASF,EAAS3H,GAClBiJ,EAAWpB,EAAOW,KAAeX,EAAOW,GAAa,IACrDU,EAAatB,EAAYqB,EAAUd,EAAiBI,EAAaxC,EAAU,MAEjF,GAAImD,EAGF,YAFAA,EAAWN,OAASM,EAAWN,QAAUA,GAK3C,MAAMlB,EAAMD,EAAYU,EAAiBE,EAAkBc,QAAQrC,EAAgB,KAC7EnC,EAAK4D,EA5Fb,SAAoCvI,EAASC,EAAU0E,GACrD,OAAO,SAASoB,EAAQmC,GACtB,MAAMkB,EAAcpJ,EAAQqJ,iBAAiBpJ,GAE7C,IAAK,IAAI+F,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOvC,WACtE,IAAK,IAAIuE,EAAIoB,EAAYhI,OAAQ4G,KAC/B,GAAIoB,EAAYpB,KAAOhC,EAQrB,OAPAkC,EAAMa,eAAiB/C,EAEnBD,EAAQ6C,QAEVU,EAAaC,IAAIvJ,EAASkI,EAAMsB,KAAMvJ,EAAU0E,GAG3CA,EAAG8E,MAAMzD,EAAQ,CAACkC,IAM/B,OAAO,MAyEPwB,CAA2B1J,EAAS+F,EAASuC,GAzGjD,SAA0BtI,EAAS2E,GACjC,OAAO,SAASoB,EAAQmC,GAOtB,OANAA,EAAMa,eAAiB/I,EAEnB+F,EAAQ6C,QACVU,EAAaC,IAAIvJ,EAASkI,EAAMsB,KAAM7E,GAGjCA,EAAG8E,MAAMzJ,EAAS,CAACkI,KAkG1ByB,CAAiB3J,EAAS+F,GAE5BpB,EAAGmD,mBAAqBS,EAAaxC,EAAU,KAC/CpB,EAAGwD,gBAAkBA,EACrBxD,EAAGiE,OAASA,EACZjE,EAAGuC,SAAWQ,EACduB,EAASvB,GAAO/C,EAEhB3E,EAAQgF,iBAAiBwD,EAAW7D,EAAI4D,GAG1C,SAASqB,EAAc5J,EAAS6H,EAAQW,EAAWzC,EAAS+B,GAC1D,MAAMnD,EAAKiD,EAAYC,EAAOW,GAAYzC,EAAS+B,GAE9CnD,IAIL3E,EAAQiG,oBAAoBuC,EAAW7D,EAAIkF,QAAQ/B,WAC5CD,EAAOW,GAAW7D,EAAGuC,WAe9B,SAASuB,EAAaP,GAGpB,OADAA,EAAQA,EAAMiB,QAAQpC,EAAgB,IAC/BI,EAAae,IAAUA,EAGhC,MAAMoB,EAAe,CACnBQ,GAAG9J,EAASkI,EAAOnC,EAASuC,GAC1BK,EAAW3I,EAASkI,EAAOnC,EAASuC,GAAc,IAGpDyB,IAAI/J,EAASkI,EAAOnC,EAASuC,GAC3BK,EAAW3I,EAASkI,EAAOnC,EAASuC,GAAc,IAGpDiB,IAAIvJ,EAASqI,EAAmBtC,EAASuC,GACvC,GAAiC,iBAAtBD,IAAmCrI,EAC5C,OAGF,MAAOuI,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBtC,EAASuC,GACvF0B,EAAcxB,IAAcH,EAC5BR,EAASF,EAAS3H,GAClBiK,EAAc5B,EAAkBhI,WAAW,KAEjD,QAA+B,IAApB8H,EAAiC,CAE1C,IAAKN,IAAWA,EAAOW,GACrB,OAIF,YADAoB,EAAc5J,EAAS6H,EAAQW,EAAWL,EAAiBI,EAAaxC,EAAU,MAIhFkE,GACFxI,OAAOC,KAAKmG,GAAQlG,QAAQuI,KAhDlC,SAAkClK,EAAS6H,EAAQW,EAAW2B,GAC5D,MAAMC,EAAoBvC,EAAOW,IAAc,GAE/C/G,OAAOC,KAAK0I,GAAmBzI,QAAQ0I,IACrC,GAAIA,EAAWjK,SAAS+J,GAAY,CAClC,MAAMjC,EAAQkC,EAAkBC,GAEhCT,EAAc5J,EAAS6H,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,uBA0CrEwC,CAAyBtK,EAAS6H,EAAQqC,EAAc7B,EAAkBkC,MAAM,MAIpF,MAAMH,EAAoBvC,EAAOW,IAAc,GAC/C/G,OAAOC,KAAK0I,GAAmBzI,QAAQ6I,IACrC,MAAMH,EAAaG,EAAYrB,QAAQnC,EAAe,IAEtD,IAAKgD,GAAe3B,EAAkBjI,SAASiK,GAAa,CAC1D,MAAMnC,EAAQkC,EAAkBI,GAEhCZ,EAAc5J,EAAS6H,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,wBAK7E2C,QAAQzK,EAASkI,EAAOwC,GACtB,GAAqB,iBAAVxC,IAAuBlI,EAChC,OAAO,KAGT,MAAMuE,EAAIV,IACJ2E,EAAYC,EAAaP,GACzB8B,EAAc9B,IAAUM,EACxBmC,EAAWpD,EAAamB,IAAIF,GAElC,IAAIoC,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIhB,GAAezF,IACjBqG,EAAcrG,EAAEzD,MAAMoH,EAAOwC,GAE7BnG,EAAEvE,GAASyK,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAMvK,SAAS2K,YAAY,cAC3BJ,EAAIK,UAAU7C,EAAWqC,GAAS,IAElCG,EAAM,IAAIM,YAAYpD,EAAO,CAC3B2C,QAAAA,EACAU,YAAY,SAKI,IAATb,GACTjJ,OAAOC,KAAKgJ,GAAM/I,QAAQ6J,IACxB/J,OAAOgK,eAAeT,EAAKQ,EAAK,CAC9BE,IAAG,IACMhB,EAAKc,OAMhBT,GACFC,EAAIW,iBAGFb,GACF9K,EAAQa,cAAcmK,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYe,iBAGPX,IC3ULY,EAAa,IAAIC,IAEvB,IAAAC,EAAe,CACbC,IAAI/L,EAASwL,EAAKQ,GACXJ,EAAWlD,IAAI1I,IAClB4L,EAAWG,IAAI/L,EAAS,IAAI6L,KAG9B,MAAMI,EAAcL,EAAWF,IAAI1L,GAI9BiM,EAAYvD,IAAI8C,IAA6B,IAArBS,EAAYC,KAMzCD,EAAYF,IAAIP,EAAKQ,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAYvK,QAAQ,QAOhIgK,IAAG,CAAC1L,EAASwL,IACPI,EAAWlD,IAAI1I,IACV4L,EAAWF,IAAI1L,GAAS0L,IAAIF,IAG9B,KAGTe,OAAOvM,EAASwL,GACd,IAAKI,EAAWlD,IAAI1I,GAClB,OAGF,MAAMiM,EAAcL,EAAWF,IAAI1L,GAEnCiM,EAAYO,OAAOhB,GAGM,IAArBS,EAAYC,MACdN,EAAWY,OAAOxM,KC/BxB,MAAMyM,EACJC,YAAY1M,IACVA,EAAUmB,EAAWnB,MAMrBgJ,KAAK2D,SAAW3M,EAChB8L,EAAKC,IAAI/C,KAAK2D,SAAU3D,KAAK0D,YAAYE,SAAU5D,OAGrD6D,UACEf,EAAKS,OAAOvD,KAAK2D,SAAU3D,KAAK0D,YAAYE,UAC5CtD,EAAaC,IAAIP,KAAK2D,SAAU3D,KAAK0D,YAAYI,WAEjDrL,OAAOsL,oBAAoB/D,MAAMrH,QAAQqL,IACvChE,KAAKgE,GAAgB,OAIzBC,eAAe3I,EAAUtE,EAASkN,GAAa,GAC7C/H,EAAuBb,EAAUtE,EAASkN,GAK1BC,mBAACnN,GACjB,OAAO8L,EAAKJ,IAAIvK,EAAWnB,GAAUgJ,KAAK4D,UAGlBO,2BAACnN,EAASuB,EAAS,IAC3C,OAAOyH,KAAKoE,YAAYpN,IAAY,IAAIgJ,KAAKhJ,EAA2B,iBAAXuB,EAAsBA,EAAS,MAG5E8L,qBAChB,MAtCY,QAyCC5I,kBACb,MAAM,IAAI6I,MAAM,uEAGCV,sBACjB,MAAQ,MAAK5D,KAAKvE,KAGAqI,uBAClB,MAAQ,IAAG9D,KAAK4D,UC5DpB,MAAMW,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUV,UACvCtI,EAAOgJ,EAAU/I,KAEvB6E,EAAaQ,GAAGrJ,SAAUiN,EAAa,qBAAoBlJ,OAAU,SAAU0D,GAK7E,GAJI,CAAC,IAAK,QAAQ9H,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,MACb,OAGF,MAAMhD,EAASrF,EAAuBqI,OAASA,KAAK4E,QAAS,IAAGpJ,GAC/CgJ,EAAUK,oBAAoB7H,GAGtCyH,SCMb,MAAMK,UAAcrB,EAGHhI,kBACb,MAnBS,QAwBXsJ,QAGE,GAFmBzE,EAAamB,QAAQzB,KAAK2D,SArB5B,kBAuBF5B,iBACb,OAGF/B,KAAK2D,SAAS5J,UAAUwJ,OAxBJ,QA0BpB,MAAMW,EAAalE,KAAK2D,SAAS5J,UAAUC,SA3BvB,QA4BpBgG,KAAKiE,eAAe,IAAMjE,KAAKgF,kBAAmBhF,KAAK2D,SAAUO,GAInEc,kBACEhF,KAAK2D,SAASJ,SACdjD,EAAamB,QAAQzB,KAAK2D,SAnCR,mBAoClB3D,KAAK6D,UAKeM,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoB7E,MAEvC,GAAsB,iBAAXzH,EAAX,CAIA,QAAqB4M,IAAjBD,EAAK3M,IAAyBA,EAAOlB,WAAW,MAAmB,gBAAXkB,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,WAWnBuE,EAAqBO,EAAO,SAQ5B1J,EAAmB0J,GC7DnB,MAAMM,UAAe3B,EAGJhI,kBACb,MArBS,SA0BX4J,SAEErF,KAAK2D,SAAS2B,aAAa,eAAgBtF,KAAK2D,SAAS5J,UAAUsL,OAvB7C,WA4BFlB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOE,EAAOP,oBAAoB7E,MAEzB,WAAXzH,GACF2M,EAAK3M,SChDb,SAASgN,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQ9I,OAAO8I,GAAKxM,WACf0D,OAAO8I,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiBjD,GACxB,OAAOA,EAAIrC,QAAQ,SAAUuF,GAAQ,IAAGA,EAAIvM,eDuC9CmH,EAAaQ,GAAGrJ,SAzCc,2BAFD,4BA2CyCyH,IACpEA,EAAMyD,iBAEN,MAAMgD,EAASzG,EAAMlC,OAAO4H,QA9CD,6BA+CdQ,EAAOP,oBAAoBc,GAEnCN,WAUPjK,EAAmBgK,GCpDnB,MAAMQ,EAAc,CAClBC,iBAAiB7O,EAASwL,EAAK1J,GAC7B9B,EAAQsO,aAAc,WAAUG,EAAiBjD,GAAQ1J,IAG3DgN,oBAAoB9O,EAASwL,GAC3BxL,EAAQ+O,gBAAiB,WAAUN,EAAiBjD,KAGtDwD,kBAAkBhP,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMiP,EAAa,GAUnB,OARAxN,OAAOC,KAAK1B,EAAQkP,SACjBC,OAAO3D,GAAOA,EAAInL,WAAW,OAC7BsB,QAAQ6J,IACP,IAAI4D,EAAU5D,EAAIrC,QAAQ,MAAO,IACjCiG,EAAUA,EAAQC,OAAO,GAAGlN,cAAgBiN,EAAQ7E,MAAM,EAAG6E,EAAQhO,QACrE6N,EAAWG,GAAWb,EAAcvO,EAAQkP,QAAQ1D,MAGjDyD,GAGTK,iBAAgB,CAACtP,EAASwL,IACjB+C,EAAcvO,EAAQE,aAAc,WAAUuO,EAAiBjD,KAGxE+D,OAAOvP,GACL,MAAMwP,EAAOxP,EAAQyP,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM3L,OAAO4L,YACvBC,KAAMJ,EAAKI,KAAO7L,OAAO8L,cAI7BC,SAAS9P,IACA,CACL0P,IAAK1P,EAAQ+P,UACbH,KAAM5P,EAAQgQ,cCzDdC,EAAiB,CACrBC,KAAI,CAACjQ,EAAUD,EAAUS,SAAS2C,kBACzB,GAAG+M,UAAUC,QAAQC,UAAUhH,iBAAiBpH,KAAKjC,EAASC,IAGvEqQ,QAAO,CAACrQ,EAAUD,EAAUS,SAAS2C,kBAC5BgN,QAAQC,UAAU3P,cAAcuB,KAAKjC,EAASC,GAGvDsQ,SAAQ,CAACvQ,EAASC,IACT,GAAGkQ,UAAUnQ,EAAQuQ,UACzBpB,OAAOqB,GAASA,EAAMC,QAAQxQ,IAGnCyQ,QAAQ1Q,EAASC,GACf,MAAMyQ,EAAU,GAEhB,IAAIC,EAAW3Q,EAAQyD,WAEvB,KAAOkN,GAAYA,EAASzP,WAAa2B,KAAKC,cArBhC,IAqBgD6N,EAASzP,UACjEyP,EAASF,QAAQxQ,IACnByQ,EAAQzL,KAAK0L,GAGfA,EAAWA,EAASlN,WAGtB,OAAOiN,GAGTE,KAAK5Q,EAASC,GACZ,IAAI4Q,EAAW7Q,EAAQ8Q,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQxQ,GACnB,MAAO,CAAC4Q,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAK/Q,EAASC,GACZ,IAAI8Q,EAAO/Q,EAAQgR,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQxQ,GACf,MAAO,CAAC8Q,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,IAGTC,kBAAkBjR,GAChB,MAAMkR,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,IAAIlR,GAAeA,EAAF,yBAAmCmR,KAAK,MAE3D,OAAOpI,KAAKkH,KAAKgB,EAAYlR,GAASmP,OAAOkC,IAAOzO,EAAWyO,IAAO7O,EAAU6O,MCjD9EC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QAElBC,EAAmB,CACvBC,UAAkBF,EAClBG,WAAmBJ,GA4CrB,MAAMK,UAAiB5F,EACrBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAKuJ,OAAS,KACdvJ,KAAKwJ,UAAY,KACjBxJ,KAAKyJ,eAAiB,KACtBzJ,KAAK0J,WAAY,EACjB1J,KAAK2J,YAAa,EAClB3J,KAAK4J,aAAe,KACpB5J,KAAK6J,YAAc,EACnB7J,KAAK8J,YAAc,EAEnB9J,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKiK,mBAAqBhD,EAAeK,QA3BjB,uBA2B8CtH,KAAK2D,UAC3E3D,KAAKkK,gBAAkB,iBAAkBzS,SAAS2C,iBAAmB+P,UAAUC,eAAiB,EAChGpK,KAAKqK,cAAgBxJ,QAAQ9F,OAAOuP,cAEpCtK,KAAKuK,qBAKWjC,qBAChB,OAAOA,EAGM7M,kBACb,MA3GS,WAgHXsM,OACE/H,KAAKwK,OAAO1B,GAGd2B,mBAGOhT,SAASiT,QAAUlR,EAAUwG,KAAK2D,WACrC3D,KAAK+H,OAITH,OACE5H,KAAKwK,OAAOzB,GAGdL,MAAMxJ,GACCA,IACHc,KAAK0J,WAAY,GAGfzC,EAAeK,QApEI,2CAoEwBtH,KAAK2D,YAClD/L,EAAqBoI,KAAK2D,UAC1B3D,KAAK2K,OAAM,IAGbC,cAAc5K,KAAKwJ,WACnBxJ,KAAKwJ,UAAY,KAGnBmB,MAAMzL,GACCA,IACHc,KAAK0J,WAAY,GAGf1J,KAAKwJ,YACPoB,cAAc5K,KAAKwJ,WACnBxJ,KAAKwJ,UAAY,MAGfxJ,KAAK+J,SAAW/J,KAAK+J,QAAQxB,WAAavI,KAAK0J,YACjD1J,KAAK6K,kBAEL7K,KAAKwJ,UAAYsB,aACdrT,SAASsT,gBAAkB/K,KAAKyK,gBAAkBzK,KAAK+H,MAAMiD,KAAKhL,MACnEA,KAAK+J,QAAQxB,WAKnB0C,GAAGzN,GACDwC,KAAKyJ,eAAiBxC,EAAeK,QArGZ,wBAqG0CtH,KAAK2D,UACxE,MAAMuH,EAAclL,KAAKmL,cAAcnL,KAAKyJ,gBAE5C,GAAIjM,EAAQwC,KAAKuJ,OAAOnR,OAAS,GAAKoF,EAAQ,EAC5C,OAGF,GAAIwC,KAAK2J,WAEP,YADArJ,EAAaS,IAAIf,KAAK2D,SApIR,mBAoI8B,IAAM3D,KAAKiL,GAAGzN,IAI5D,GAAI0N,IAAgB1N,EAGlB,OAFAwC,KAAK0I,aACL1I,KAAK2K,QAIP,MAAMS,EAAQ5N,EAAQ0N,EACpBpC,EACAC,EAEF/I,KAAKwK,OAAOY,EAAOpL,KAAKuJ,OAAO/L,IAKjCwM,WAAWzR,GAOT,OANAA,EAAS,IACJ+P,KACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EApMS,WAoMaE,EAAQsQ,GACvBtQ,EAGT8S,eACE,MAAMC,EAAY3N,KAAK4N,IAAIvL,KAAK8J,aAEhC,GAAIwB,GAnMgB,GAoMlB,OAGF,MAAME,EAAYF,EAAYtL,KAAK8J,YAEnC9J,KAAK8J,YAAc,EAEd0B,GAILxL,KAAKwK,OAAOgB,EAAY,EAAIvC,EAAkBD,GAGhDuB,qBACMvK,KAAK+J,QAAQvB,UACflI,EAAaQ,GAAGd,KAAK2D,SApLJ,sBAoL6BzE,GAASc,KAAKyL,SAASvM,IAG5C,UAAvBc,KAAK+J,QAAQrB,QACfpI,EAAaQ,GAAGd,KAAK2D,SAvLD,yBAuL6BzE,GAASc,KAAK0I,MAAMxJ,IACrEoB,EAAaQ,GAAGd,KAAK2D,SAvLD,yBAuL6BzE,GAASc,KAAK2K,MAAMzL,KAGnEc,KAAK+J,QAAQnB,OAAS5I,KAAKkK,iBAC7BlK,KAAK0L,0BAITA,0BACE,MAAMC,EAAQzM,KACRc,KAAKqK,eAnKU,QAmKQnL,EAAM0M,aApKZ,UAoKgD1M,EAAM0M,YAE/D5L,KAAKqK,gBACfrK,KAAK6J,YAAc3K,EAAM2M,QAAQ,GAAGC,SAFpC9L,KAAK6J,YAAc3K,EAAM4M,SAMvBC,EAAO7M,IAEXc,KAAK8J,YAAc5K,EAAM2M,SAAW3M,EAAM2M,QAAQzT,OAAS,EACzD,EACA8G,EAAM2M,QAAQ,GAAGC,QAAU9L,KAAK6J,aAG9BmC,EAAM9M,KACNc,KAAKqK,eAlLU,QAkLQnL,EAAM0M,aAnLZ,UAmLgD1M,EAAM0M,cACzE5L,KAAK8J,YAAc5K,EAAM4M,QAAU9L,KAAK6J,aAG1C7J,KAAKqL,eACsB,UAAvBrL,KAAK+J,QAAQrB,QASf1I,KAAK0I,QACD1I,KAAK4J,cACPqC,aAAajM,KAAK4J,cAGpB5J,KAAK4J,aAAe1M,WAAWgC,GAASc,KAAK2K,MAAMzL,GAtQ5B,IAsQ6Dc,KAAK+J,QAAQxB,YAIrGtB,EAAeC,KAjNO,qBAiNiBlH,KAAK2D,UAAUhL,QAAQuT,IAC5D5L,EAAaQ,GAAGoL,EAlOI,wBAkOuBC,GAAKA,EAAExJ,oBAGhD3C,KAAKqK,eACP/J,EAAaQ,GAAGd,KAAK2D,SAxOA,0BAwO6BzE,GAASyM,EAAMzM,IACjEoB,EAAaQ,GAAGd,KAAK2D,SAxOF,wBAwO6BzE,GAAS8M,EAAI9M,IAE7Dc,KAAK2D,SAAS5J,UAAUqS,IA9NG,mBAgO3B9L,EAAaQ,GAAGd,KAAK2D,SAhPD,yBAgP6BzE,GAASyM,EAAMzM,IAChEoB,EAAaQ,GAAGd,KAAK2D,SAhPF,wBAgP6BzE,GAAS6M,EAAK7M,IAC9DoB,EAAaQ,GAAGd,KAAK2D,SAhPH,uBAgP6BzE,GAAS8M,EAAI9M,KAIhEuM,SAASvM,GACP,GAAI,kBAAkB7F,KAAK6F,EAAMlC,OAAO2H,SACtC,OAGF,MAAM6G,EAAYtC,EAAiBhK,EAAMsD,KACrCgJ,IACFtM,EAAMyD,iBACN3C,KAAKwK,OAAOgB,IAIhBL,cAAcnU,GAKZ,OAJAgJ,KAAKuJ,OAASvS,GAAWA,EAAQyD,WAC/BwM,EAAeC,KAhPC,iBAgPmBlQ,EAAQyD,YAC3C,GAEKuF,KAAKuJ,OAAO9L,QAAQzG,GAG7BqV,gBAAgBjB,EAAO/N,GACrB,MAAMiP,EAASlB,IAAUtC,EACzB,OAAO3L,EAAqB6C,KAAKuJ,OAAQlM,EAAeiP,EAAQtM,KAAK+J,QAAQpB,MAG/E4D,mBAAmBzM,EAAe0M,GAChC,MAAMC,EAAczM,KAAKmL,cAAcrL,GACjC4M,EAAY1M,KAAKmL,cAAclE,EAAeK,QA9P3B,wBA8PyDtH,KAAK2D,WAEvF,OAAOrD,EAAamB,QAAQzB,KAAK2D,SAxRhB,oBAwRuC,CACtD7D,cAAAA,EACA0L,UAAWgB,EACXlJ,KAAMoJ,EACNzB,GAAIwB,IAIRE,2BAA2B3V,GACzB,GAAIgJ,KAAKiK,mBAAoB,CAC3B,MAAM2C,EAAkB3F,EAAeK,QA3QrB,UA2Q8CtH,KAAKiK,oBAErE2C,EAAgB7S,UAAUwJ,OArRN,UAsRpBqJ,EAAgB7G,gBAAgB,gBAEhC,MAAM8G,EAAa5F,EAAeC,KA1Qb,mBA0QsClH,KAAKiK,oBAEhE,IAAK,IAAIjL,EAAI,EAAGA,EAAI6N,EAAWzU,OAAQ4G,IACrC,GAAItC,OAAOoQ,SAASD,EAAW7N,GAAG9H,aAAa,oBAAqB,MAAQ8I,KAAKmL,cAAcnU,GAAU,CACvG6V,EAAW7N,GAAGjF,UAAUqS,IA5RR,UA6RhBS,EAAW7N,GAAGsG,aAAa,eAAgB,QAC3C,QAMRuF,kBACE,MAAM7T,EAAUgJ,KAAKyJ,gBAAkBxC,EAAeK,QA5R7B,wBA4R2DtH,KAAK2D,UAEzF,IAAK3M,EACH,OAGF,MAAM+V,EAAkBrQ,OAAOoQ,SAAS9V,EAAQE,aAAa,oBAAqB,IAE9E6V,GACF/M,KAAK+J,QAAQiD,gBAAkBhN,KAAK+J,QAAQiD,iBAAmBhN,KAAK+J,QAAQxB,SAC5EvI,KAAK+J,QAAQxB,SAAWwE,GAExB/M,KAAK+J,QAAQxB,SAAWvI,KAAK+J,QAAQiD,iBAAmBhN,KAAK+J,QAAQxB,SAIzEiC,OAAOyC,EAAkBjW,GACvB,MAAMoU,EAAQpL,KAAKkN,kBAAkBD,GAC/B5P,EAAgB4J,EAAeK,QA9SZ,wBA8S0CtH,KAAK2D,UAClEwJ,EAAqBnN,KAAKmL,cAAc9N,GACxC+P,EAAcpW,GAAWgJ,KAAKqM,gBAAgBjB,EAAO/N,GAErDgQ,EAAmBrN,KAAKmL,cAAciC,GACtCE,EAAYzM,QAAQb,KAAKwJ,WAEzB8C,EAASlB,IAAUtC,EACnByE,EAAuBjB,EA5TR,sBADF,oBA8TbkB,EAAiBlB,EA5TH,qBACA,qBA4TdE,EAAqBxM,KAAKyN,kBAAkBrC,GAElD,GAAIgC,GAAeA,EAAYrT,UAAUC,SAnUnB,UAqUpB,YADAgG,KAAK2J,YAAa,GAIpB,GAAI3J,KAAK2J,WACP,OAIF,GADmB3J,KAAKuM,mBAAmBa,EAAaZ,GACzCzK,iBACb,OAGF,IAAK1E,IAAkB+P,EAErB,OAGFpN,KAAK2J,YAAa,EAEd2D,GACFtN,KAAK0I,QAGP1I,KAAK2M,2BAA2BS,GAChCpN,KAAKyJ,eAAiB2D,EAEtB,MAAMM,EAAmB,KACvBpN,EAAamB,QAAQzB,KAAK2D,SA9WZ,mBA8WkC,CAC9C7D,cAAesN,EACf5B,UAAWgB,EACXlJ,KAAM6J,EACNlC,GAAIoC,KAIR,GAAIrN,KAAK2D,SAAS5J,UAAUC,SAvWP,SAuWmC,CACtDoT,EAAYrT,UAAUqS,IAAIoB,GAE1B7S,EAAOyS,GAEP/P,EAActD,UAAUqS,IAAImB,GAC5BH,EAAYrT,UAAUqS,IAAImB,GAE1B,MAAMI,EAAmB,KACvBP,EAAYrT,UAAUwJ,OAAOgK,EAAsBC,GACnDJ,EAAYrT,UAAUqS,IAlXJ,UAoXlB/O,EAActD,UAAUwJ,OApXN,SAoXgCiK,EAAgBD,GAElEvN,KAAK2J,YAAa,EAElBzM,WAAWwQ,EAAkB,IAG/B1N,KAAKiE,eAAe0J,EAAkBtQ,GAAe,QAErDA,EAActD,UAAUwJ,OA7XJ,UA8XpB6J,EAAYrT,UAAUqS,IA9XF,UAgYpBpM,KAAK2J,YAAa,EAClB+D,IAGEJ,GACFtN,KAAK2K,QAITuC,kBAAkB1B,GAChB,MAAK,CAACvC,EAAiBD,GAAgB5R,SAASoU,GAI5CtQ,IACKsQ,IAAcxC,EAAiBD,EAAaD,EAG9C0C,IAAcxC,EAAiBF,EAAaC,EAP1CyC,EAUXiC,kBAAkBrC,GAChB,MAAK,CAACtC,EAAYC,GAAY3R,SAASgU,GAInClQ,IACKkQ,IAAUrC,EAAaC,EAAiBC,EAG1CmC,IAAUrC,EAAaE,EAAkBD,EAPvCoC,EAYajH,yBAACnN,EAASuB,GAChC,MAAM2M,EAAOmE,EAASxE,oBAAoB7N,EAASuB,GAEnD,IAAIwR,QAAEA,GAAY7E,EACI,iBAAX3M,IACTwR,EAAU,IACLA,KACAxR,IAIP,MAAMqV,EAA2B,iBAAXrV,EAAsBA,EAASwR,EAAQtB,MAE7D,GAAsB,iBAAXlQ,EACT2M,EAAK+F,GAAG1S,QACH,GAAsB,iBAAXqV,EAAqB,CACrC,QAA4B,IAAjB1I,EAAK0I,GACd,MAAM,IAAItU,UAAW,oBAAmBsU,MAG1C1I,EAAK0I,UACI7D,EAAQxB,UAAYwB,EAAQ8D,OACrC3I,EAAKwD,QACLxD,EAAKyF,SAIaxG,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACfoE,EAASyE,kBAAkB9N,KAAMzH,MAIX4L,2BAACjF,GACzB,MAAMlC,EAASrF,EAAuBqI,MAEtC,IAAKhD,IAAWA,EAAOjD,UAAUC,SAxcT,YAyctB,OAGF,MAAMzB,EAAS,IACVqN,EAAYI,kBAAkBhJ,MAC9B4I,EAAYI,kBAAkBhG,OAE7B+N,EAAa/N,KAAK9I,aAAa,oBAEjC6W,IACFxV,EAAOgQ,UAAW,GAGpBc,EAASyE,kBAAkB9Q,EAAQzE,GAE/BwV,GACF1E,EAASjF,YAAYpH,GAAQiO,GAAG8C,GAGlC7O,EAAMyD,kBAUVrC,EAAaQ,GAAGrJ,SAxec,6BAkBF,sCAsdyC4R,EAAS2E,qBAE9E1N,EAAaQ,GAAG/F,OA3ea,4BA2egB,KAC3C,MAAMkT,EAAYhH,EAAeC,KAxdR,6BA0dzB,IAAK,IAAIlI,EAAI,EAAGC,EAAMgP,EAAU7V,OAAQ4G,EAAIC,EAAKD,IAC/CqK,EAASyE,kBAAkBG,EAAUjP,GAAIqK,EAASjF,YAAY6J,EAAUjP,OAW5E5D,EAAmBiO,GC5iBnB,MAKMf,EAAU,CACdjD,QAAQ,EACR6I,OAAQ,MAGJrF,GAAc,CAClBxD,OAAQ,UACR6I,OAAQ,kBA2BV,MAAMC,WAAiB1K,EACrBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAKoO,kBAAmB,EACxBpO,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKqO,cAAgB,GAErB,MAAMC,EAAarH,EAAeC,KAhBT,+BAkBzB,IAAK,IAAIlI,EAAI,EAAGC,EAAMqP,EAAWlW,OAAQ4G,EAAIC,EAAKD,IAAK,CACrD,MAAMuP,EAAOD,EAAWtP,GAClB/H,EAAWO,EAAuB+W,GAClCC,EAAgBvH,EAAeC,KAAKjQ,GACvCkP,OAAOsI,GAAaA,IAAczO,KAAK2D,UAEzB,OAAb1M,GAAqBuX,EAAcpW,SACrC4H,KAAK0O,UAAYzX,EACjB+I,KAAKqO,cAAcpS,KAAKsS,IAI5BvO,KAAK2O,sBAEA3O,KAAK+J,QAAQmE,QAChBlO,KAAK4O,0BAA0B5O,KAAKqO,cAAerO,KAAK6O,YAGtD7O,KAAK+J,QAAQ1E,QACfrF,KAAKqF,SAMSiD,qBAChB,OAAOA,EAGM7M,kBACb,MA/ES,WAoFX4J,SACMrF,KAAK6O,WACP7O,KAAK8O,OAEL9O,KAAK+O,OAITA,OACE,GAAI/O,KAAKoO,kBAAoBpO,KAAK6O,WAChC,OAGF,IACIG,EADAC,EAAU,GAGd,GAAIjP,KAAK+J,QAAQmE,OAAQ,CACvB,MAAM3G,EAAWN,EAAeC,KAAM,sBAAkDlH,KAAK+J,QAAQmE,QACrGe,EAAUhI,EAAeC,KAxEN,qBAwE6BlH,KAAK+J,QAAQmE,QAAQ/H,OAAOoI,IAAShH,EAASnQ,SAASmX,IAGzG,MAAMW,EAAYjI,EAAeK,QAAQtH,KAAK0O,WAC9C,GAAIO,EAAQ7W,OAAQ,CAClB,MAAM+W,EAAiBF,EAAQ/H,KAAKqH,GAAQW,IAAcX,GAG1D,GAFAS,EAAcG,EAAiBhB,GAAS/J,YAAY+K,GAAkB,KAElEH,GAAeA,EAAYZ,iBAC7B,OAKJ,GADmB9N,EAAamB,QAAQzB,KAAK2D,SApG7B,oBAqGD5B,iBACb,OAGFkN,EAAQtW,QAAQyW,IACVF,IAAcE,GAChBjB,GAAStJ,oBAAoBuK,EAAY,CAAE/J,QAAQ,IAASyJ,OAGzDE,GACHlM,EAAKC,IAAIqM,EA7HA,cA6HsB,QAInC,MAAMC,EAAYrP,KAAKsP,gBAEvBtP,KAAK2D,SAAS5J,UAAUwJ,OA9GA,YA+GxBvD,KAAK2D,SAAS5J,UAAUqS,IA9GE,cAgH1BpM,KAAK2D,SAAS4L,MAAMF,GAAa,EAEjCrP,KAAK4O,0BAA0B5O,KAAKqO,eAAe,GACnDrO,KAAKoO,kBAAmB,EAExB,MAYMoB,EAAc,UADSH,EAAU,GAAG9V,cAAgB8V,EAAU9N,MAAM,IAG1EvB,KAAKiE,eAdY,KACfjE,KAAKoO,kBAAmB,EAExBpO,KAAK2D,SAAS5J,UAAUwJ,OAxHA,cAyHxBvD,KAAK2D,SAAS5J,UAAUqS,IA1HF,WADJ,QA6HlBpM,KAAK2D,SAAS4L,MAAMF,GAAa,GAEjC/O,EAAamB,QAAQzB,KAAK2D,SApIX,sBA0Ia3D,KAAK2D,UAAU,GAC7C3D,KAAK2D,SAAS4L,MAAMF,GAAgBrP,KAAK2D,SAAS6L,GAAhB,KAGpCV,OACE,GAAI9O,KAAKoO,mBAAqBpO,KAAK6O,WACjC,OAIF,GADmBvO,EAAamB,QAAQzB,KAAK2D,SAlJ7B,oBAmJD5B,iBACb,OAGF,MAAMsN,EAAYrP,KAAKsP,gBAEvBtP,KAAK2D,SAAS4L,MAAMF,GAAgBrP,KAAK2D,SAAS8C,wBAAwB4I,GAAxC,KAElC1U,EAAOqF,KAAK2D,UAEZ3D,KAAK2D,SAAS5J,UAAUqS,IAvJE,cAwJ1BpM,KAAK2D,SAAS5J,UAAUwJ,OAzJA,WADJ,QA4JpB,MAAMkM,EAAqBzP,KAAKqO,cAAcjW,OAC9C,IAAK,IAAI4G,EAAI,EAAGA,EAAIyQ,EAAoBzQ,IAAK,CAC3C,MAAMyC,EAAUzB,KAAKqO,cAAcrP,GAC7BuP,EAAO5W,EAAuB8J,GAEhC8M,IAASvO,KAAK6O,SAASN,IACzBvO,KAAK4O,0BAA0B,CAACnN,IAAU,GAI9CzB,KAAKoO,kBAAmB,EASxBpO,KAAK2D,SAAS4L,MAAMF,GAAa,GAEjCrP,KAAKiE,eATY,KACfjE,KAAKoO,kBAAmB,EACxBpO,KAAK2D,SAAS5J,UAAUwJ,OAxKA,cAyKxBvD,KAAK2D,SAAS5J,UAAUqS,IA1KF,YA2KtB9L,EAAamB,QAAQzB,KAAK2D,SA/KV,uBAoLY3D,KAAK2D,UAAU,GAG/CkL,SAAS7X,EAAUgJ,KAAK2D,UACtB,OAAO3M,EAAQ+C,UAAUC,SArLL,QA0LtBgQ,WAAWzR,GAST,OARAA,EAAS,IACJ+P,KACA1C,EAAYI,kBAAkBhG,KAAK2D,aACnCpL,IAEE8M,OAASxE,QAAQtI,EAAO8M,QAC/B9M,EAAO2V,OAAS/V,EAAWI,EAAO2V,QAClC7V,EAvNS,WAuNaE,EAAQsQ,IACvBtQ,EAGT+W,gBACE,OAAOtP,KAAK2D,SAAS5J,UAAUC,SAnML,uBAEhB,QACC,SAmMb2U,sBACE,IAAK3O,KAAK+J,QAAQmE,OAChB,OAGF,MAAM3G,EAAWN,EAAeC,KAAM,sBAAkDlH,KAAK+J,QAAQmE,QACrGjH,EAAeC,KAtMU,8BAsMiBlH,KAAK+J,QAAQmE,QAAQ/H,OAAOoI,IAAShH,EAASnQ,SAASmX,IAC9F5V,QAAQ3B,IACP,MAAM0Y,EAAW/X,EAAuBX,GAEpC0Y,GACF1P,KAAK4O,0BAA0B,CAAC5X,GAAUgJ,KAAK6O,SAASa,MAKhEd,0BAA0Be,EAAcC,GACjCD,EAAavX,QAIlBuX,EAAahX,QAAQ4V,IACfqB,EACFrB,EAAKxU,UAAUwJ,OA9NM,aAgOrBgL,EAAKxU,UAAUqS,IAhOM,aAmOvBmC,EAAKjJ,aAAa,gBAAiBsK,KAMjBzL,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAM8E,EAAU,GACM,iBAAXxR,GAAuB,YAAYc,KAAKd,KACjDwR,EAAQ1E,QAAS,GAGnB,MAAMH,EAAOiJ,GAAStJ,oBAAoB7E,KAAM+J,GAEhD,GAAsB,iBAAXxR,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAYb+H,EAAaQ,GAAGrJ,SAxQc,6BAYD,+BA4PyC,SAAUyH,IAEjD,MAAzBA,EAAMlC,OAAO2H,SAAoBzF,EAAMa,gBAAmD,MAAjCb,EAAMa,eAAe4E,UAChFzF,EAAMyD,iBAGR,MAAM1L,EAAWO,EAAuBwI,MACfiH,EAAeC,KAAKjQ,GAE5B0B,QAAQ3B,IACvBmX,GAAStJ,oBAAoB7N,EAAS,CAAEqO,QAAQ,IAASA,cAW7DjK,EAAmB+S,IC3UZ,IAAIzH,GAAM,MACNmJ,GAAS,SACTC,GAAQ,QACRlJ,GAAO,OAEPmJ,GAAiB,CAACrJ,GAAKmJ,GAAQC,GAAOlJ,IAOtCoJ,GAAmCD,GAAeE,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAI/I,OAAO,CAACgJ,EAAAA,SAAyBA,EAAAA,WAC3C,IACQC,GAA0B,GAAGjJ,OAAO4I,GAAgB,CAX7C,SAWqDE,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAI/I,OAAO,CAACgJ,EAAWA,EAAAA,SAAyBA,EAAAA,WACtD,IAaQE,GAAiB,CAXJ,aACN,OACK,YAEC,aACN,OACK,YAEE,cACN,QACK,cC7BT,SAASC,GAAYtZ,GAClC,OAAOA,GAAWA,EAAQuZ,UAAY,IAAIpX,cAAgB,KCD7C,SAASqX,GAAUC,GAChC,GAAY,MAARA,EACF,OAAO1V,OAGT,GAAwB,oBAApB0V,EAAKzX,WAAkC,CACzC,IAAI0X,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwB5V,OAG/D,OAAO0V,ECRT,SAAS1Y,GAAU0Y,GAEjB,OAAOA,aADUD,GAAUC,GAAMrJ,SACIqJ,aAAgBrJ,QAGvD,SAASwJ,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YAGvD,SAASC,GAAaL,GAEpB,MAA0B,oBAAfjW,aAKJiW,aADUD,GAAUC,GAAMjW,YACIiW,aAAgBjW,YCyDvD,IAAAuW,GAAe,CACbvV,KAAM,cACNwV,SAAS,EACTC,MAAO,QACPtV,GA5EF,SAAqBuV,GACnB,IAAIC,EAAQD,EAAKC,MACjB1Y,OAAOC,KAAKyY,EAAMC,UAAUzY,SAAQ,SAAU6C,GAC5C,IAAI+T,EAAQ4B,EAAME,OAAO7V,IAAS,GAC9ByK,EAAakL,EAAMlL,WAAWzK,IAAS,GACvCxE,EAAUma,EAAMC,SAAS5V,GAExBoV,GAAc5Z,IAAasZ,GAAYtZ,KAO5CyB,OAAO6Y,OAAOta,EAAQuY,MAAOA,GAC7B9W,OAAOC,KAAKuN,GAAYtN,SAAQ,SAAU6C,GACxC,IAAI1C,EAAQmN,EAAWzK,IAET,IAAV1C,EACF9B,EAAQ+O,gBAAgBvK,GAExBxE,EAAQsO,aAAa9J,GAAgB,IAAV1C,EAAiB,GAAKA,WAwDvDyY,OAlDF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MACdM,EAAgB,CAClBC,OAAQ,CACN5K,SAAUqK,EAAMQ,QAAQC,SACxBhL,KAAM,IACNF,IAAK,IACLmL,OAAQ,KAEVC,MAAO,CACLhL,SAAU,YAEZiL,UAAW,IASb,OAPAtZ,OAAO6Y,OAAOH,EAAMC,SAASM,OAAOnC,MAAOkC,EAAcC,QACzDP,EAAME,OAASI,EAEXN,EAAMC,SAASU,OACjBrZ,OAAO6Y,OAAOH,EAAMC,SAASU,MAAMvC,MAAOkC,EAAcK,OAGnD,WACLrZ,OAAOC,KAAKyY,EAAMC,UAAUzY,SAAQ,SAAU6C,GAC5C,IAAIxE,EAAUma,EAAMC,SAAS5V,GACzByK,EAAakL,EAAMlL,WAAWzK,IAAS,GAGvC+T,EAFkB9W,OAAOC,KAAKyY,EAAME,OAAOW,eAAexW,GAAQ2V,EAAME,OAAO7V,GAAQiW,EAAcjW,IAE7EyU,QAAO,SAAUV,EAAO3W,GAElD,OADA2W,EAAM3W,GAAY,GACX2W,IACN,IAEEqB,GAAc5Z,IAAasZ,GAAYtZ,KAI5CyB,OAAO6Y,OAAOta,EAAQuY,MAAOA,GAC7B9W,OAAOC,KAAKuN,GAAYtN,SAAQ,SAAUsZ,GACxCjb,EAAQ+O,gBAAgBkM,YAa9BC,SAAU,CAAC,kBCjFE,SAASC,GAAiBhC,GACvC,OAAOA,EAAU7Y,MAAM,KAAK,GCD9B,IAAI8a,GAAQzU,KAAKyU,MACF,SAAS3L,GAAsBzP,EAASqb,QAChC,IAAjBA,IACFA,GAAe,GAGjB,IAAI7L,EAAOxP,EAAQyP,wBACf6L,EAAS,EACTC,EAAS,EAQb,OANI3B,GAAc5Z,IAAYqb,IAE5BC,EAAS9L,EAAKgM,MAAQxb,EAAQyb,aAAe,EAC7CF,EAAS/L,EAAKkM,OAAS1b,EAAQ4D,cAAgB,GAG1C,CACL4X,MAAOJ,GAAM5L,EAAKgM,MAAQF,GAC1BI,OAAQN,GAAM5L,EAAKkM,OAASH,GAC5B7L,IAAK0L,GAAM5L,EAAKE,IAAM6L,GACtBzC,MAAOsC,GAAM5L,EAAKsJ,MAAQwC,GAC1BzC,OAAQuC,GAAM5L,EAAKqJ,OAAS0C,GAC5B3L,KAAMwL,GAAM5L,EAAKI,KAAO0L,GACxBK,EAAGP,GAAM5L,EAAKI,KAAO0L,GACrBM,EAAGR,GAAM5L,EAAKE,IAAM6L,ICtBT,SAASM,GAAc7b,GACpC,IAAI8b,EAAarM,GAAsBzP,GAGnCwb,EAAQxb,EAAQyb,YAChBC,EAAS1b,EAAQ4D,aAUrB,OARI+C,KAAK4N,IAAIuH,EAAWN,MAAQA,IAAU,IACxCA,EAAQM,EAAWN,OAGjB7U,KAAK4N,IAAIuH,EAAWJ,OAASA,IAAW,IAC1CA,EAASI,EAAWJ,QAGf,CACLC,EAAG3b,EAAQgQ,WACX4L,EAAG5b,EAAQ+P,UACXyL,MAAOA,EACPE,OAAQA,GCrBG,SAAS1Y,GAASkU,EAAQ1G,GACvC,IAAIuL,EAAWvL,EAAMlN,aAAekN,EAAMlN,cAE1C,GAAI4T,EAAOlU,SAASwN,GAClB,OAAO,EAEJ,GAAIuL,GAAYjC,GAAaiC,GAAW,CACzC,IAAIhL,EAAOP,EAEX,EAAG,CACD,GAAIO,GAAQmG,EAAO8E,WAAWjL,GAC5B,OAAO,EAITA,EAAOA,EAAKtN,YAAcsN,EAAKkL,WACxBlL,GAIb,OAAO,ECpBM,SAASrO,GAAiB1C,GACvC,OAAOwZ,GAAUxZ,GAAS0C,iBAAiB1C,GCD9B,SAASkc,GAAelc,GACrC,MAAO,CAAC,QAAS,KAAM,MAAMyG,QAAQ6S,GAAYtZ,KAAa,ECDjD,SAASmc,GAAmBnc,GAEzC,QAASe,GAAUf,GAAWA,EAAQ0Z,cACtC1Z,EAAQS,WAAasD,OAAOtD,UAAU2C,gBCDzB,SAASgZ,GAAcpc,GACpC,MAA6B,SAAzBsZ,GAAYtZ,GACPA,EAMPA,EAAQqc,cACRrc,EAAQyD,aACRqW,GAAa9Z,GAAWA,EAAQic,KAAO,OAEvCE,GAAmBnc,GCRvB,SAASsc,GAAoBtc,GAC3B,OAAK4Z,GAAc5Z,IACoB,UAAvC0C,GAAiB1C,GAAS8P,SAInB9P,EAAQuc,aAHN,KAwCI,SAASC,GAAgBxc,GAItC,IAHA,IAAI+D,EAASyV,GAAUxZ,GACnBuc,EAAeD,GAAoBtc,GAEhCuc,GAAgBL,GAAeK,IAA6D,WAA5C7Z,GAAiB6Z,GAAczM,UACpFyM,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9BjD,GAAYiD,IAA0D,SAA9BjD,GAAYiD,IAAwE,WAA5C7Z,GAAiB6Z,GAAczM,UAC3H/L,EAGFwY,GA5CT,SAA4Bvc,GAC1B,IAAIyc,GAAsE,IAA1DtJ,UAAUuJ,UAAUva,cAAcsE,QAAQ,WAG1D,IAFuD,IAA5C0M,UAAUuJ,UAAUjW,QAAQ,YAE3BmT,GAAc5Z,IAII,UAFX0C,GAAiB1C,GAEnB8P,SACb,OAAO,KAMX,IAFA,IAAI6M,EAAcP,GAAcpc,GAEzB4Z,GAAc+C,IAAgB,CAAC,OAAQ,QAAQlW,QAAQ6S,GAAYqD,IAAgB,GAAG,CAC3F,IAAIC,EAAMla,GAAiBia,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAetW,QAAQmW,EAAII,aAAsBP,GAAgC,WAAnBG,EAAII,YAA2BP,GAAaG,EAAIzN,QAAyB,SAAfyN,EAAIzN,OACjO,OAAOwN,EAEPA,EAAcA,EAAYlZ,WAI9B,OAAO,KAiBgBwZ,CAAmBjd,IAAY+D,EC9DzC,SAASmZ,GAAyB/D,GAC/C,MAAO,CAAC,MAAO,UAAU1S,QAAQ0S,IAAc,EAAI,IAAM,ICDpD,IAAIvS,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACXuU,GAAQzU,KAAKyU,MCDT,SAAS+B,GAAOtW,EAAK/E,EAAO8E,GACzC,OAAOwW,GAAQvW,EAAKwW,GAAQvb,EAAO8E,ICDtB,SAAS0W,GAAmBC,GACzC,OAAO9b,OAAO6Y,OAAO,GCDd,CACL5K,IAAK,EACLoJ,MAAO,EACPD,OAAQ,EACRjJ,KAAM,GDHuC2N,GEFlC,SAASC,GAAgB1b,EAAOJ,GAC7C,OAAOA,EAAKuX,QAAO,SAAUwE,EAASjS,GAEpC,OADAiS,EAAQjS,GAAO1J,EACR2b,IACN,ICwFL,IAAAC,GAAe,CACblZ,KAAM,QACNwV,SAAS,EACTC,MAAO,OACPtV,GA9EF,SAAeuV,GACb,IAAIyD,EAEAxD,EAAQD,EAAKC,MACb3V,EAAO0V,EAAK1V,KACZmW,EAAUT,EAAKS,QACfiD,EAAezD,EAAMC,SAASU,MAC9B+C,EAAgB1D,EAAM2D,cAAcD,cACpCE,EAAgB5C,GAAiBhB,EAAMhB,WACvC6E,EAAOd,GAAyBa,GAEhC9V,EADa,CAAC2H,GAAMkJ,IAAOrS,QAAQsX,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBU,EAAS9D,GAItD,OAAOmD,GAAsC,iBAH7CW,EAA6B,mBAAZA,EAAyBA,EAAQxc,OAAO6Y,OAAO,GAAIH,EAAM+D,MAAO,CAC/E/E,UAAWgB,EAAMhB,aACb8E,GACkDA,EAAUT,GAAgBS,EAASlF,KAoBvEoF,CAAgBxD,EAAQsD,QAAS9D,GACjDiE,EAAYvC,GAAc+B,GAC1BS,EAAmB,MAATL,EAAetO,GAAME,GAC/B0O,EAAmB,MAATN,EAAenF,GAASC,GAClCyF,EAAUpE,EAAM+D,MAAMnD,UAAU9S,GAAOkS,EAAM+D,MAAMnD,UAAUiD,GAAQH,EAAcG,GAAQ7D,EAAM+D,MAAMxD,OAAOzS,GAC9GuW,EAAYX,EAAcG,GAAQ7D,EAAM+D,MAAMnD,UAAUiD,GACxDS,EAAoBjC,GAAgBoB,GACpCc,EAAaD,EAA6B,MAATT,EAAeS,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9C3X,EAAM0W,EAAcc,GACpBzX,EAAM8X,EAAaN,EAAUnW,GAAOsV,EAAce,GAClDQ,EAASJ,EAAa,EAAIN,EAAUnW,GAAO,EAAI4W,EAC/CtP,EAAS4N,GAAOtW,EAAKiY,EAAQlY,GAE7BmY,EAAWf,EACf7D,EAAM2D,cAActZ,KAASmZ,EAAwB,IAA0BoB,GAAYxP,EAAQoO,EAAsBqB,aAAezP,EAASuP,EAAQnB,KA6CzJpD,OA1CF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MAEd8E,EADUzE,EAAMG,QACW3a,QAC3B4d,OAAoC,IAArBqB,EAA8B,sBAAwBA,EAErD,MAAhBrB,IAKwB,iBAAjBA,IACTA,EAAezD,EAAMC,SAASM,OAAOha,cAAckd,MAahD5a,GAASmX,EAAMC,SAASM,OAAQkD,KAQrCzD,EAAMC,SAASU,MAAQ8C,IAUvB1C,SAAU,CAAC,iBACXgE,iBAAkB,CAAC,oBC3FjBC,GAAa,CACfzP,IAAK,OACLoJ,MAAO,OACPD,OAAQ,OACRjJ,KAAM,QAgBD,SAASwP,GAAY5E,GAC1B,IAAI6E,EAEA3E,EAASF,EAAME,OACf4E,EAAa9E,EAAM8E,WACnBnG,EAAYqB,EAAMrB,UAClBoG,EAAU/E,EAAM+E,QAChBzP,EAAW0K,EAAM1K,SACjB0P,EAAkBhF,EAAMgF,gBACxBC,EAAWjF,EAAMiF,SACjBC,EAAelF,EAAMkF,aAErBC,GAAyB,IAAjBD,EAvBd,SAA2BxF,GACzB,IAAIyB,EAAIzB,EAAKyB,EACTC,EAAI1B,EAAK0B,EAETgE,EADM7b,OACI8b,kBAAoB,EAClC,MAAO,CACLlE,EAAGP,GAAMA,GAAMO,EAAIiE,GAAOA,IAAQ,EAClChE,EAAGR,GAAMA,GAAMQ,EAAIgE,GAAOA,IAAQ,GAgBAE,CAAkBP,GAAmC,mBAAjBG,EAA8BA,EAAaH,GAAWA,EAC1HQ,EAAUJ,EAAMhE,EAChBA,OAAgB,IAAZoE,EAAqB,EAAIA,EAC7BC,EAAUL,EAAM/D,EAChBA,OAAgB,IAAZoE,EAAqB,EAAIA,EAE7BC,EAAOV,EAAQvE,eAAe,KAC9BkF,EAAOX,EAAQvE,eAAe,KAC9BmF,EAAQvQ,GACRwQ,EAAQ1Q,GACR2Q,EAAMtc,OAEV,GAAI0b,EAAU,CACZ,IAAIlD,EAAeC,GAAgB9B,GAC/B4F,EAAa,eACbC,EAAY,cAEZhE,IAAiB/C,GAAUkB,IAGmB,WAA5ChY,GAFJ6Z,EAAeJ,GAAmBzB,IAEC5K,WACjCwQ,EAAa,eACbC,EAAY,eAKhBhE,EAAeA,EAEXpD,IAAczJ,KAChB0Q,EAAQvH,GAER+C,GAAKW,EAAa+D,GAAchB,EAAW5D,OAC3CE,GAAK4D,EAAkB,GAAK,GAG1BrG,IAAcvJ,KAChBuQ,EAAQrH,GAER6C,GAAKY,EAAagE,GAAajB,EAAW9D,MAC1CG,GAAK6D,EAAkB,GAAK,GAIhC,IAKMgB,EALFC,EAAehf,OAAO6Y,OAAO,CAC/BxK,SAAUA,GACT2P,GAAYN,IAEf,OAAIK,EAGK/d,OAAO6Y,OAAO,GAAImG,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe3D,WAAawD,EAAIR,kBAAoB,GAAK,EAAI,aAAelE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAU4E,IAG3R/e,OAAO6Y,OAAO,GAAImG,IAAepB,EAAkB,IAAoBe,GAASF,EAAOtE,EAAI,KAAO,GAAIyD,EAAgBc,GAASF,EAAOtE,EAAI,KAAO,GAAI0D,EAAgBxC,UAAY,GAAIwC,IAsD9L,IAAAqB,GAAe,CACblc,KAAM,gBACNwV,SAAS,EACTC,MAAO,cACPtV,GAvDF,SAAuBgc,GACrB,IAAIxG,EAAQwG,EAAMxG,MACdQ,EAAUgG,EAAMhG,QAChBiG,EAAwBjG,EAAQ6E,gBAChCA,OAA4C,IAA1BoB,GAA0CA,EAC5DC,EAAoBlG,EAAQ8E,SAC5BA,OAAiC,IAAtBoB,GAAsCA,EACjDC,EAAwBnG,EAAQ+E,aAChCA,OAAyC,IAA1BoB,GAA0CA,EAYzDL,EAAe,CACjBtH,UAAWgC,GAAiBhB,EAAMhB,WAClCuB,OAAQP,EAAMC,SAASM,OACvB4E,WAAYnF,EAAM+D,MAAMxD,OACxB8E,gBAAiBA,GAGsB,MAArCrF,EAAM2D,cAAcD,gBACtB1D,EAAME,OAAOK,OAASjZ,OAAO6Y,OAAO,GAAIH,EAAME,OAAOK,OAAQ0E,GAAY3d,OAAO6Y,OAAO,GAAImG,EAAc,CACvGlB,QAASpF,EAAM2D,cAAcD,cAC7B/N,SAAUqK,EAAMQ,QAAQC,SACxB6E,SAAUA,EACVC,aAAcA,OAIe,MAA7BvF,EAAM2D,cAAchD,QACtBX,EAAME,OAAOS,MAAQrZ,OAAO6Y,OAAO,GAAIH,EAAME,OAAOS,MAAOsE,GAAY3d,OAAO6Y,OAAO,GAAImG,EAAc,CACrGlB,QAASpF,EAAM2D,cAAchD,MAC7BhL,SAAU,WACV2P,UAAU,EACVC,aAAcA,OAIlBvF,EAAMlL,WAAWyL,OAASjZ,OAAO6Y,OAAO,GAAIH,EAAMlL,WAAWyL,OAAQ,CACnEqG,wBAAyB5G,EAAMhB,aAUjCjL,KAAM,ICvJJ8S,GAAU,CACZA,SAAS,GAsCXC,GAAe,CACbzc,KAAM,iBACNwV,SAAS,EACTC,MAAO,QACPtV,GAAI,aACJ4V,OAxCF,SAAgBL,GACd,IAAIC,EAAQD,EAAKC,MACbnO,EAAWkO,EAAKlO,SAChB2O,EAAUT,EAAKS,QACfuG,EAAkBvG,EAAQwG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkBzG,EAAQ0G,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7Crd,EAASyV,GAAUW,EAAMC,SAASM,QAClC4G,EAAgB,GAAGnR,OAAOgK,EAAMmH,cAAcvG,UAAWZ,EAAMmH,cAAc5G,QAYjF,OAVIyG,GACFG,EAAc3f,SAAQ,SAAU4f,GAC9BA,EAAavc,iBAAiB,SAAUgH,EAASwV,OAAQR,OAIzDK,GACFtd,EAAOiB,iBAAiB,SAAUgH,EAASwV,OAAQR,IAG9C,WACDG,GACFG,EAAc3f,SAAQ,SAAU4f,GAC9BA,EAAatb,oBAAoB,SAAU+F,EAASwV,OAAQR,OAI5DK,GACFtd,EAAOkC,oBAAoB,SAAU+F,EAASwV,OAAQR,MAY1D9S,KAAM,IC/CJuT,GAAO,CACT7R,KAAM,QACNkJ,MAAO,OACPD,OAAQ,MACRnJ,IAAK,UAEQ,SAASgS,GAAqBvI,GAC3C,OAAOA,EAAUhQ,QAAQ,0BAA0B,SAAUwY,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACT9M,MAAO,MACPK,IAAK,SAEQ,SAAS4M,GAA8BzI,GACpD,OAAOA,EAAUhQ,QAAQ,cAAc,SAAUwY,GAC/C,OAAOF,GAAKE,MCLD,SAASE,GAAgBpI,GACtC,IAAI4G,EAAM7G,GAAUC,GAGpB,MAAO,CACLqI,WAHezB,EAAIxQ,YAInBkS,UAHc1B,EAAI1Q,aCDP,SAASqS,GAAoBhiB,GAQ1C,OAAOyP,GAAsB0M,GAAmBnc,IAAU4P,KAAOiS,GAAgB7hB,GAAS8hB,WCV7E,SAASG,GAAejiB,GAErC,IAAIkiB,EAAoBxf,GAAiB1C,GACrCmiB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6BhgB,KAAK8f,EAAWE,EAAYD,GCGnD,SAASE,GAAkBtiB,EAASoG,GACjD,IAAImc,OAES,IAATnc,IACFA,EAAO,IAGT,IAAImb,ECdS,SAASiB,EAAgB/I,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAahT,QAAQ6S,GAAYG,KAAU,EAEvDA,EAAKC,cAAc1V,KAGxB4V,GAAcH,IAASwI,GAAexI,GACjCA,EAGF+I,EAAgBpG,GAAc3C,IDIlB+I,CAAgBxiB,GAC/ByiB,EAASlB,KAAqE,OAAlDgB,EAAwBviB,EAAQ0Z,oBAAyB,EAAS6I,EAAsBve,MACpHqc,EAAM7G,GAAU+H,GAChBvb,EAASyc,EAAS,CAACpC,GAAKlQ,OAAOkQ,EAAIqC,gBAAkB,GAAIT,GAAeV,GAAgBA,EAAe,IAAMA,EAC7GoB,EAAcvc,EAAK+J,OAAOnK,GAC9B,OAAOyc,EAASE,EAChBA,EAAYxS,OAAOmS,GAAkBlG,GAAcpW,KExBtC,SAAS4c,GAAiBpT,GACvC,OAAO/N,OAAO6Y,OAAO,GAAI9K,EAAM,CAC7BI,KAAMJ,EAAKmM,EACXjM,IAAKF,EAAKoM,EACV9C,MAAOtJ,EAAKmM,EAAInM,EAAKgM,MACrB3C,OAAQrJ,EAAKoM,EAAIpM,EAAKkM,SCuB1B,SAASmH,GAA2B7iB,EAAS8iB,GAC3C,M/BpBoB,a+BoBbA,EAA8BF,GC1BxB,SAAyB5iB,GACtC,IAAIqgB,EAAM7G,GAAUxZ,GAChB+iB,EAAO5G,GAAmBnc,GAC1B0iB,EAAiBrC,EAAIqC,eACrBlH,EAAQuH,EAAKnE,YACblD,EAASqH,EAAKpE,aACdhD,EAAI,EACJC,EAAI,EAuBR,OAjBI8G,IACFlH,EAAQkH,EAAelH,MACvBE,EAASgH,EAAehH,OASnB,iCAAiCrZ,KAAK8Q,UAAUuJ,aACnDf,EAAI+G,EAAe1S,WACnB4L,EAAI8G,EAAe3S,YAIhB,CACLyL,MAAOA,EACPE,OAAQA,EACRC,EAAGA,EAAIqG,GAAoBhiB,GAC3B4b,EAAGA,GDRiDoH,CAAgBhjB,IAAY4Z,GAAckJ,GAdlG,SAAoC9iB,GAClC,IAAIwP,EAAOC,GAAsBzP,GASjC,OARAwP,EAAKE,IAAMF,EAAKE,IAAM1P,EAAQijB,UAC9BzT,EAAKI,KAAOJ,EAAKI,KAAO5P,EAAQkjB,WAChC1T,EAAKqJ,OAASrJ,EAAKE,IAAM1P,EAAQ2e,aACjCnP,EAAKsJ,MAAQtJ,EAAKI,KAAO5P,EAAQ4e,YACjCpP,EAAKgM,MAAQxb,EAAQ4e,YACrBpP,EAAKkM,OAAS1b,EAAQ2e,aACtBnP,EAAKmM,EAAInM,EAAKI,KACdJ,EAAKoM,EAAIpM,EAAKE,IACPF,EAI2G2T,CAA2BL,GAAkBF,GEtBlJ,SAAyB5iB,GACtC,IAAIuiB,EAEAQ,EAAO5G,GAAmBnc,GAC1BojB,EAAYvB,GAAgB7hB,GAC5BgE,EAA0D,OAAlDue,EAAwBviB,EAAQ0Z,oBAAyB,EAAS6I,EAAsBve,KAChGwX,EAAQ5U,GAAImc,EAAKM,YAAaN,EAAKnE,YAAa5a,EAAOA,EAAKqf,YAAc,EAAGrf,EAAOA,EAAK4a,YAAc,GACvGlD,EAAS9U,GAAImc,EAAKO,aAAcP,EAAKpE,aAAc3a,EAAOA,EAAKsf,aAAe,EAAGtf,EAAOA,EAAK2a,aAAe,GAC5GhD,GAAKyH,EAAUtB,WAAaE,GAAoBhiB,GAChD4b,GAAKwH,EAAUrB,UAMnB,MAJiD,QAA7Crf,GAAiBsB,GAAQ+e,GAAMvO,YACjCmH,GAAK/U,GAAImc,EAAKnE,YAAa5a,EAAOA,EAAK4a,YAAc,GAAKpD,GAGrD,CACLA,MAAOA,EACPE,OAAQA,EACRC,EAAGA,EACHC,EAAGA,GFG2K2H,CAAgBpH,GAAmBnc,KG7BtM,SAASwjB,GAAarK,GACnC,OAAOA,EAAU7Y,MAAM,KAAK,GCGf,SAASmjB,GAAevJ,GACrC,IAOIqF,EAPAxE,EAAYb,EAAKa,UACjB/a,EAAUka,EAAKla,QACfmZ,EAAYe,EAAKf,UACjB4E,EAAgB5E,EAAYgC,GAAiBhC,GAAa,KAC1DuK,EAAYvK,EAAYqK,GAAarK,GAAa,KAClDwK,EAAU5I,EAAUY,EAAIZ,EAAUS,MAAQ,EAAIxb,EAAQwb,MAAQ,EAC9DoI,EAAU7I,EAAUa,EAAIb,EAAUW,OAAS,EAAI1b,EAAQ0b,OAAS,EAGpE,OAAQqC,GACN,KAAKrO,GACH6P,EAAU,CACR5D,EAAGgI,EACH/H,EAAGb,EAAUa,EAAI5b,EAAQ0b,QAE3B,MAEF,KAAK7C,GACH0G,EAAU,CACR5D,EAAGgI,EACH/H,EAAGb,EAAUa,EAAIb,EAAUW,QAE7B,MAEF,KAAK5C,GACHyG,EAAU,CACR5D,EAAGZ,EAAUY,EAAIZ,EAAUS,MAC3BI,EAAGgI,GAEL,MAEF,KAAKhU,GACH2P,EAAU,CACR5D,EAAGZ,EAAUY,EAAI3b,EAAQwb,MACzBI,EAAGgI,GAEL,MAEF,QACErE,EAAU,CACR5D,EAAGZ,EAAUY,EACbC,EAAGb,EAAUa,GAInB,IAAIiI,EAAW9F,EAAgBb,GAAyBa,GAAiB,KAEzE,GAAgB,MAAZ8F,EAAkB,CACpB,IAAI5b,EAAmB,MAAb4b,EAAmB,SAAW,QAExC,OAAQH,GACN,InClDa,QmCmDXnE,EAAQsE,GAAYtE,EAAQsE,IAAa9I,EAAU9S,GAAO,EAAIjI,EAAQiI,GAAO,GAC7E,MAEF,InCrDW,MmCsDTsX,EAAQsE,GAAYtE,EAAQsE,IAAa9I,EAAU9S,GAAO,EAAIjI,EAAQiI,GAAO,IAOnF,OAAOsX,EC1DM,SAASuE,GAAe3J,EAAOQ,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXqJ,EAAqBD,EAAS5K,UAC9BA,OAAmC,IAAvB6K,EAAgC7J,EAAMhB,UAAY6K,EAC9DC,EAAoBF,EAASG,SAC7BA,OAAiC,IAAtBD,EpCXY,kBoCWqCA,EAC5DE,EAAwBJ,EAASK,aACjCA,OAAyC,IAA1BD,EpCZC,WoCY6CA,EAC7DE,EAAwBN,EAASO,eACjCA,OAA2C,IAA1BD,EpCbH,SoCa+CA,EAC7DE,EAAuBR,EAASS,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBV,EAAS9F,QAC5BA,OAA+B,IAArBwG,EAA8B,EAAIA,EAC5ClH,EAAgBD,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAASlF,KACpG2L,EpCnBc,WoCmBDJ,EpClBI,YADH,SoCoBdK,EAAmBxK,EAAMC,SAASW,UAClCuE,EAAanF,EAAM+D,MAAMxD,OACzB1a,EAAUma,EAAMC,SAASoK,EAAcE,EAAaJ,GACpDM,ELmBS,SAAyB5kB,EAASkkB,EAAUE,GACzD,IAAIS,EAAmC,oBAAbX,EAlB5B,SAA4BlkB,GAC1B,IAAI8kB,EAAkBxC,GAAkBlG,GAAcpc,IAElD+kB,EADoB,CAAC,WAAY,SAASte,QAAQ/D,GAAiB1C,GAAS8P,WAAa,GACnD8J,GAAc5Z,GAAWwc,GAAgBxc,GAAWA,EAE9F,OAAKe,GAAUgkB,GAKRD,EAAgB3V,QAAO,SAAU2T,GACtC,OAAO/hB,GAAU+hB,IAAmB9f,GAAS8f,EAAgBiC,IAAmD,SAAhCzL,GAAYwJ,MALrF,GAYkDkC,CAAmBhlB,GAAW,GAAGmQ,OAAO+T,GAC/FY,EAAkB,GAAG3U,OAAO0U,EAAqB,CAACT,IAClDa,EAAsBH,EAAgB,GACtCI,EAAeJ,EAAgB7L,QAAO,SAAUkM,EAASrC,GAC3D,IAAItT,EAAOqT,GAA2B7iB,EAAS8iB,GAK/C,OAJAqC,EAAQzV,IAAM9I,GAAI4I,EAAKE,IAAKyV,EAAQzV,KACpCyV,EAAQrM,MAAQjS,GAAI2I,EAAKsJ,MAAOqM,EAAQrM,OACxCqM,EAAQtM,OAAShS,GAAI2I,EAAKqJ,OAAQsM,EAAQtM,QAC1CsM,EAAQvV,KAAOhJ,GAAI4I,EAAKI,KAAMuV,EAAQvV,MAC/BuV,IACNtC,GAA2B7iB,EAASilB,IAKvC,OAJAC,EAAa1J,MAAQ0J,EAAapM,MAAQoM,EAAatV,KACvDsV,EAAaxJ,OAASwJ,EAAarM,OAASqM,EAAaxV,IACzDwV,EAAavJ,EAAIuJ,EAAatV,KAC9BsV,EAAatJ,EAAIsJ,EAAaxV,IACvBwV,EKnCkBE,CAAgBrkB,GAAUf,GAAWA,EAAUA,EAAQqlB,gBAAkBlJ,GAAmBhC,EAAMC,SAASM,QAASwJ,EAAUE,GACnJkB,EAAsB7V,GAAsBkV,GAC5C9G,EAAgB4F,GAAe,CACjC1I,UAAWuK,EACXtlB,QAASsf,EACT1E,SAAU,WACVzB,UAAWA,IAEToM,EAAmB3C,GAAiBnhB,OAAO6Y,OAAO,GAAIgF,EAAYzB,IAClE2H,EpChCc,WoCgCMlB,EAA4BiB,EAAmBD,EAGnEG,EAAkB,CACpB/V,IAAKkV,EAAmBlV,IAAM8V,EAAkB9V,IAAM6N,EAAc7N,IACpEmJ,OAAQ2M,EAAkB3M,OAAS+L,EAAmB/L,OAAS0E,EAAc1E,OAC7EjJ,KAAMgV,EAAmBhV,KAAO4V,EAAkB5V,KAAO2N,EAAc3N,KACvEkJ,MAAO0M,EAAkB1M,MAAQ8L,EAAmB9L,MAAQyE,EAAczE,OAExE4M,EAAavL,EAAM2D,cAAcvO,OAErC,GpC3CkB,WoC2Cd+U,GAA6BoB,EAAY,CAC3C,IAAInW,EAASmW,EAAWvM,GACxB1X,OAAOC,KAAK+jB,GAAiB9jB,SAAQ,SAAU6J,GAC7C,IAAIma,EAAW,CAAC7M,GAAOD,IAAQpS,QAAQ+E,IAAQ,EAAI,GAAK,EACpDwS,EAAO,CAACtO,GAAKmJ,IAAQpS,QAAQ+E,IAAQ,EAAI,IAAM,IACnDia,EAAgBja,IAAQ+D,EAAOyO,GAAQ2H,KAI3C,OAAOF,EC1DM,SAASG,GAAqBzL,EAAOQ,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXxB,EAAY4K,EAAS5K,UACrB+K,EAAWH,EAASG,SACpBE,EAAeL,EAASK,aACxBnG,EAAU8F,EAAS9F,QACnB4H,EAAiB9B,EAAS8B,eAC1BC,EAAwB/B,EAASgC,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EpC,EAAYF,GAAarK,GACzBC,EAAasK,EAAYmC,EAAiB7M,GAAsBA,GAAoB7J,QAAO,SAAUgK,GACvG,OAAOqK,GAAarK,KAAeuK,KAChC3K,GACDkN,EAAoB7M,EAAWjK,QAAO,SAAUgK,GAClD,OAAO4M,EAAsBtf,QAAQ0S,IAAc,KAGpB,IAA7B8M,EAAkB7kB,SACpB6kB,EAAoB7M,GAQtB,IAAI8M,EAAYD,EAAkBhN,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAa2K,GAAe3J,EAAO,CACrChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdnG,QAASA,IACR9C,GAAiBhC,IACbD,IACN,IACH,OAAOzX,OAAOC,KAAKwkB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MC6FpC,IAAAC,GAAe,CACb9hB,KAAM,OACNwV,SAAS,EACTC,MAAO,OACPtV,GA5HF,SAAcuV,GACZ,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfnW,EAAO0V,EAAK1V,KAEhB,IAAI2V,EAAM2D,cAActZ,GAAM+hB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB7L,EAAQkJ,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB/L,EAAQgM,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BlM,EAAQmM,mBACtC7I,EAAUtD,EAAQsD,QAClBiG,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBuC,EAAwBpM,EAAQkL,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBpL,EAAQoL,sBAChCiB,EAAqB7M,EAAMQ,QAAQxB,UACnC4E,EAAgB5C,GAAiB6L,GAEjCF,EAAqBD,IADH9I,IAAkBiJ,GACqCnB,EAjC/E,SAAuC1M,GACrC,GtCLgB,SsCKZgC,GAAiBhC,GACnB,MAAO,GAGT,IAAI8N,EAAoBvF,GAAqBvI,GAC7C,MAAO,CAACyI,GAA8BzI,GAAY8N,EAAmBrF,GAA8BqF,IA2BwCC,CAA8BF,GAA3E,CAACtF,GAAqBsF,KAChH5N,EAAa,CAAC4N,GAAoB7W,OAAO2W,GAAoB7N,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAI/I,OtCvCG,SsCuCIgL,GAAiBhC,GAAsByM,GAAqBzL,EAAO,CACnFhB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdnG,QAASA,EACT4H,eAAgBA,EAChBE,sBAAuBA,IACpB5M,KACJ,IACCgO,EAAgBhN,EAAM+D,MAAMnD,UAC5BuE,EAAanF,EAAM+D,MAAMxD,OACzB0M,EAAY,IAAIvb,IAChBwb,GAAqB,EACrBC,EAAwBlO,EAAW,GAE9BpR,EAAI,EAAGA,EAAIoR,EAAWhY,OAAQ4G,IAAK,CAC1C,IAAImR,EAAYC,EAAWpR,GAEvBuf,EAAiBpM,GAAiBhC,GAElCqO,EtCzDW,UsCyDQhE,GAAarK,GAChCsO,EAAa,CAAC/X,GAAKmJ,IAAQpS,QAAQ8gB,IAAmB,EACtDtf,EAAMwf,EAAa,QAAU,SAC7BtF,EAAW2B,GAAe3J,EAAO,CACnChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACbvG,QAASA,IAEPyJ,EAAoBD,EAAaD,EAAmB1O,GAAQlJ,GAAO4X,EAAmB3O,GAASnJ,GAE/FyX,EAAclf,GAAOqX,EAAWrX,KAClCyf,EAAoBhG,GAAqBgG,IAG3C,IAAIC,EAAmBjG,GAAqBgG,GACxCE,EAAS,GAUb,GARInB,GACFmB,EAAO3iB,KAAKkd,EAASoF,IAAmB,GAGtCX,GACFgB,EAAO3iB,KAAKkd,EAASuF,IAAsB,EAAGvF,EAASwF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFR,EAAwBnO,EACxBkO,GAAqB,EACrB,MAGFD,EAAUrb,IAAIoN,EAAWyO,GAG3B,GAAIP,EAqBF,IAnBA,IAEIU,EAAQ,SAAeC,GACzB,IAAIC,EAAmB7O,EAAWlJ,MAAK,SAAUiJ,GAC/C,IAAIyO,EAASR,EAAU1b,IAAIyN,GAE3B,GAAIyO,EACF,OAAOA,EAAOrd,MAAM,EAAGyd,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAX,EAAwBW,EACjB,SAIFD,EAnBYnC,EAAiB,EAAI,EAmBZmC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpC7N,EAAMhB,YAAcmO,IACtBnN,EAAM2D,cAActZ,GAAM+hB,OAAQ,EAClCpM,EAAMhB,UAAYmO,EAClBnN,EAAM+N,OAAQ,KAUhBhJ,iBAAkB,CAAC,UACnBhR,KAAM,CACJqY,OAAO,IC7IX,SAAS4B,GAAehG,EAAU3S,EAAM4Y,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjBzM,EAAG,EACHC,EAAG,IAIA,CACLlM,IAAKyS,EAASzS,IAAMF,EAAKkM,OAAS0M,EAAiBxM,EACnD9C,MAAOqJ,EAASrJ,MAAQtJ,EAAKgM,MAAQ4M,EAAiBzM,EACtD9C,OAAQsJ,EAAStJ,OAASrJ,EAAKkM,OAAS0M,EAAiBxM,EACzDhM,KAAMuS,EAASvS,KAAOJ,EAAKgM,MAAQ4M,EAAiBzM,GAIxD,SAAS0M,GAAsBlG,GAC7B,MAAO,CAACzS,GAAKoJ,GAAOD,GAAQjJ,IAAM0Y,MAAK,SAAUC,GAC/C,OAAOpG,EAASoG,IAAS,KAiC7B,IAAAC,GAAe,CACbhkB,KAAM,OACNwV,SAAS,EACTC,MAAO,OACPiF,iBAAkB,CAAC,mBACnBva,GAlCF,SAAcuV,GACZ,IAAIC,EAAQD,EAAKC,MACb3V,EAAO0V,EAAK1V,KACZ2iB,EAAgBhN,EAAM+D,MAAMnD,UAC5BuE,EAAanF,EAAM+D,MAAMxD,OACzB0N,EAAmBjO,EAAM2D,cAAc2K,gBACvCC,EAAoB5E,GAAe3J,EAAO,CAC5CmK,eAAgB,cAEdqE,EAAoB7E,GAAe3J,EAAO,CAC5CqK,aAAa,IAEXoE,EAA2BT,GAAeO,EAAmBvB,GAC7D0B,EAAsBV,GAAeQ,EAAmBrJ,EAAY8I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7C1O,EAAM2D,cAActZ,GAAQ,CAC1BokB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpB5O,EAAMlL,WAAWyL,OAASjZ,OAAO6Y,OAAO,GAAIH,EAAMlL,WAAWyL,OAAQ,CACnEsO,+BAAgCF,EAChCG,sBAAuBF,MCH3BG,GAAe,CACb1kB,KAAM,SACNwV,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACXvW,GA5BF,SAAgB6V,GACd,IAAIL,EAAQK,EAAML,MACdQ,EAAUH,EAAMG,QAChBnW,EAAOgW,EAAMhW,KACb2kB,EAAkBxO,EAAQpL,OAC1BA,OAA6B,IAApB4Z,EAA6B,CAAC,EAAG,GAAKA,EAC/Cjb,EAAOkL,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAW+E,EAAO3O,GACxD,IAAIwO,EAAgB5C,GAAiBhC,GACjCiQ,EAAiB,CAACxZ,GAAMF,IAAKjJ,QAAQsX,IAAkB,GAAK,EAAI,EAEhE7D,EAAyB,mBAAX3K,EAAwBA,EAAO9N,OAAO6Y,OAAO,GAAI4D,EAAO,CACxE/E,UAAWA,KACP5J,EACF8Z,EAAWnP,EAAK,GAChBoP,EAAWpP,EAAK,GAIpB,OAFAmP,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACxZ,GAAMkJ,IAAOrS,QAAQsX,IAAkB,EAAI,CACjDpC,EAAG2N,EACH1N,EAAGyN,GACD,CACF1N,EAAG0N,EACHzN,EAAG0N,GAWcC,CAAwBpQ,EAAWgB,EAAM+D,MAAO3O,GAC1D2J,IACN,IACCsQ,EAAwBtb,EAAKiM,EAAMhB,WACnCwC,EAAI6N,EAAsB7N,EAC1BC,EAAI4N,EAAsB5N,EAEW,MAArCzB,EAAM2D,cAAcD,gBACtB1D,EAAM2D,cAAcD,cAAclC,GAAKA,EACvCxB,EAAM2D,cAAcD,cAAcjC,GAAKA,GAGzCzB,EAAM2D,cAActZ,GAAQ0J,ICxB9Bub,GAAe,CACbjlB,KAAM,gBACNwV,SAAS,EACTC,MAAO,OACPtV,GApBF,SAAuBuV,GACrB,IAAIC,EAAQD,EAAKC,MACb3V,EAAO0V,EAAK1V,KAKhB2V,EAAM2D,cAActZ,GAAQif,GAAe,CACzC1I,UAAWZ,EAAM+D,MAAMnD,UACvB/a,QAASma,EAAM+D,MAAMxD,OACrBE,SAAU,WACVzB,UAAWgB,EAAMhB,aAUnBjL,KAAM,IC6FRwb,GAAe,CACbllB,KAAM,kBACNwV,SAAS,EACTC,MAAO,OACPtV,GA5GF,SAAyBuV,GACvB,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfnW,EAAO0V,EAAK1V,KACZgiB,EAAoB7L,EAAQkJ,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB/L,EAAQgM,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDxC,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBvG,EAAUtD,EAAQsD,QAClB0L,EAAkBhP,EAAQiP,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBlP,EAAQmP,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtD1H,EAAW2B,GAAe3J,EAAO,CACnC+J,SAAUA,EACVE,aAAcA,EACdnG,QAASA,EACTuG,YAAaA,IAEXzG,EAAgB5C,GAAiBhB,EAAMhB,WACvCuK,EAAYF,GAAarJ,EAAMhB,WAC/B4Q,GAAmBrG,EACnBG,EAAW3G,GAAyBa,GACpC4I,ECrCY,MDqCS9C,ECrCH,IAAM,IDsCxBhG,EAAgB1D,EAAM2D,cAAcD,cACpCsJ,EAAgBhN,EAAM+D,MAAMnD,UAC5BuE,EAAanF,EAAM+D,MAAMxD,OACzBsP,EAA4C,mBAAjBF,EAA8BA,EAAaroB,OAAO6Y,OAAO,GAAIH,EAAM+D,MAAO,CACvG/E,UAAWgB,EAAMhB,aACb2Q,EACF5b,EAAO,CACTyN,EAAG,EACHC,EAAG,GAGL,GAAKiC,EAAL,CAIA,GAAI4I,GAAiBG,EAAc,CACjC,IAAIqD,EAAwB,MAAbpG,EAAmBnU,GAAME,GACpCsa,EAAuB,MAAbrG,EAAmBhL,GAASC,GACtC7Q,EAAmB,MAAb4b,EAAmB,SAAW,QACpCtU,EAASsO,EAAcgG,GACvBhd,EAAMgX,EAAcgG,GAAY1B,EAAS8H,GACzCrjB,EAAMiX,EAAcgG,GAAY1B,EAAS+H,GACzCC,EAAWP,GAAUtK,EAAWrX,GAAO,EAAI,EAC3CmiB,E1CxDW,U0CwDF1G,EAAsByD,EAAclf,GAAOqX,EAAWrX,GAC/DoiB,E1CzDW,U0CyDF3G,GAAuBpE,EAAWrX,IAAQkf,EAAclf,GAGjE2V,EAAezD,EAAMC,SAASU,MAC9BsD,EAAYwL,GAAUhM,EAAe/B,GAAc+B,GAAgB,CACrEpC,MAAO,EACPE,OAAQ,GAEN4O,EAAqBnQ,EAAM2D,cAAc,oBAAsB3D,EAAM2D,cAAc,oBAAoBG,QxBtEtG,CACLvO,IAAK,EACLoJ,MAAO,EACPD,OAAQ,EACRjJ,KAAM,GwBmEF2a,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWtN,GAAO,EAAGgK,EAAclf,GAAMmW,EAAUnW,IACnDyiB,EAAYX,EAAkB5C,EAAclf,GAAO,EAAIkiB,EAAWM,EAAWF,EAAkBP,EAAoBI,EAASK,EAAWF,EAAkBP,EACzJW,EAAYZ,GAAmB5C,EAAclf,GAAO,EAAIkiB,EAAWM,EAAWD,EAAkBR,EAAoBK,EAASI,EAAWD,EAAkBR,EAC1JvL,EAAoBtE,EAAMC,SAASU,OAAS0B,GAAgBrC,EAAMC,SAASU,OAC3E8P,EAAenM,EAAiC,MAAboF,EAAmBpF,EAAkBwE,WAAa,EAAIxE,EAAkByE,YAAc,EAAI,EAC7H2H,EAAsB1Q,EAAM2D,cAAcvO,OAAS4K,EAAM2D,cAAcvO,OAAO4K,EAAMhB,WAAW0K,GAAY,EAC3GiH,EAAYjN,EAAcgG,GAAY6G,EAAYG,EAAsBD,EACxEG,EAAYlN,EAAcgG,GAAY8G,EAAYE,EAEtD,GAAIpE,EAAe,CACjB,IAAIuE,EAAkB7N,GAAOyM,EAASvM,GAAQxW,EAAKikB,GAAajkB,EAAK0I,EAAQqa,EAASxM,GAAQxW,EAAKmkB,GAAankB,GAChHiX,EAAcgG,GAAYmH,EAC1B9c,EAAK2V,GAAYmH,EAAkBzb,EAGrC,GAAIqX,EAAc,CAChB,IAAIqE,EAAyB,MAAbpH,EAAmBnU,GAAME,GAErCsb,EAAwB,MAAbrH,EAAmBhL,GAASC,GAEvCqS,EAAUtN,EAAc8I,GAExByE,EAAOD,EAAUhJ,EAAS8I,GAE1BI,GAAOF,EAAUhJ,EAAS+I,GAE1BI,GAAmBnO,GAAOyM,EAASvM,GAAQ+N,EAAMN,GAAaM,EAAMD,EAASvB,EAASxM,GAAQiO,GAAMN,GAAaM,IAErHxN,EAAc8I,GAAW2E,GACzBpd,EAAKyY,GAAW2E,GAAmBH,GAIvChR,EAAM2D,cAActZ,GAAQ0J,IAS5BgR,iBAAkB,CAAC,WExGN,SAASqM,GAAiBC,EAAyBjP,EAAckP,QAC9D,IAAZA,IACFA,GAAU,GAGZ,IClBoChS,ECJOzZ,EFsBvC0rB,EAA0B9R,GAAc2C,GACxCoP,EAAuB/R,GAAc2C,IAf3C,SAAyBvc,GACvB,IAAIwP,EAAOxP,EAAQyP,wBACf6L,EAAS9L,EAAKgM,MAAQxb,EAAQyb,aAAe,EAC7CF,EAAS/L,EAAKkM,OAAS1b,EAAQ4D,cAAgB,EACnD,OAAkB,IAAX0X,GAA2B,IAAXC,EAWmCqQ,CAAgBrP,GACtEnZ,EAAkB+Y,GAAmBI,GACrC/M,EAAOC,GAAsB+b,EAAyBG,GACtDxK,EAAS,CACXW,WAAY,EACZC,UAAW,GAETxC,EAAU,CACZ5D,EAAG,EACHC,EAAG,GAkBL,OAfI8P,IAA4BA,IAA4BD,MACxB,SAA9BnS,GAAYiD,IAChB0F,GAAe7e,MACb+d,GClCgC1H,EDkCT8C,KCjCd/C,GAAUC,IAAUG,GAAcH,GCJxC,CACLqI,YAFyC9hB,EDQbyZ,GCNRqI,WACpBC,UAAW/hB,EAAQ+hB,WDGZF,GAAgBpI,IDmCnBG,GAAc2C,KAChBgD,EAAU9P,GAAsB8M,GAAc,IACtCZ,GAAKY,EAAa2G,WAC1B3D,EAAQ3D,GAAKW,EAAa0G,WACjB7f,IACTmc,EAAQ5D,EAAIqG,GAAoB5e,KAI7B,CACLuY,EAAGnM,EAAKI,KAAOuR,EAAOW,WAAavC,EAAQ5D,EAC3CC,EAAGpM,EAAKE,IAAMyR,EAAOY,UAAYxC,EAAQ3D,EACzCJ,MAAOhM,EAAKgM,MACZE,OAAQlM,EAAKkM,QGtCjB,IAAImQ,GAAkB,CACpB1S,UAAW,SACX2S,UAAW,GACXlR,SAAU,YAGZ,SAASmR,KACP,IAAK,IAAIC,EAAOC,UAAU7qB,OAAQsJ,EAAO,IAAI2B,MAAM2f,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ExhB,EAAKwhB,GAAQD,UAAUC,GAGzB,OAAQxhB,EAAK4d,MAAK,SAAUtoB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQyP,0BAIhC,SAAS0c,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCX,GAAkBW,EAC3E,OAAO,SAAsBzR,EAAWL,EAAQC,QAC9B,IAAZA,IACFA,EAAU8R,GAGZ,IC/C6B9nB,EAC3B+nB,ED8CEvS,EAAQ,CACVhB,UAAW,SACXwT,iBAAkB,GAClBhS,QAASlZ,OAAO6Y,OAAO,GAAIuR,GAAiBY,GAC5C3O,cAAe,GACf1D,SAAU,CACRW,UAAWA,EACXL,OAAQA,GAEVzL,WAAY,GACZoL,OAAQ,IAENuS,EAAmB,GACnBC,GAAc,EACd7gB,EAAW,CACbmO,MAAOA,EACP2S,WAAY,SAAoBnS,GAC9BoS,IACA5S,EAAMQ,QAAUlZ,OAAO6Y,OAAO,GAAImS,EAAgBtS,EAAMQ,QAASA,GACjER,EAAMmH,cAAgB,CACpBvG,UAAWha,GAAUga,GAAauH,GAAkBvH,GAAaA,EAAUsK,eAAiB/C,GAAkBvH,EAAUsK,gBAAkB,GAC1I3K,OAAQ4H,GAAkB5H,IAI5B,IExE4BoR,EAC9BkB,EFuEML,EGtCG,SAAwBb,GAErC,IAAIa,EAlCN,SAAeb,GACb,IAAI3a,EAAM,IAAItF,IACVohB,EAAU,IAAIzlB,IACd0lB,EAAS,GA0Bb,OAzBApB,EAAUnqB,SAAQ,SAAUwrB,GAC1Bhc,EAAIpF,IAAIohB,EAAS3oB,KAAM2oB,MAkBzBrB,EAAUnqB,SAAQ,SAAUwrB,GACrBF,EAAQvkB,IAAIykB,EAAS3oB,OAhB5B,SAAS2hB,EAAKgH,GACZF,EAAQ7X,IAAI+X,EAAS3oB,MACN,GAAG2L,OAAOgd,EAASjS,UAAY,GAAIiS,EAASjO,kBAAoB,IACtEvd,SAAQ,SAAUyrB,GACzB,IAAKH,EAAQvkB,IAAI0kB,GAAM,CACrB,IAAIC,EAAclc,EAAIzF,IAAI0hB,GAEtBC,GACFlH,EAAKkH,OAIXH,EAAOjoB,KAAKkoB,GAMVhH,CAAKgH,MAGFD,EAKgB9Y,CAAM0X,GAE7B,OAAOzS,GAAeJ,QAAO,SAAUC,EAAKe,GAC1C,OAAOf,EAAI/I,OAAOwc,EAAiBxd,QAAO,SAAUge,GAClD,OAAOA,EAASlT,QAAUA,QAE3B,IH8B0BqT,EExEKxB,EFwEsB,GAAG3b,OAAOoc,EAAkBpS,EAAMQ,QAAQmR,WEvE9FkB,EAASlB,EAAU7S,QAAO,SAAU+T,EAAQO,GAC9C,IAAIC,EAAWR,EAAOO,EAAQ/oB,MAK9B,OAJAwoB,EAAOO,EAAQ/oB,MAAQgpB,EAAW/rB,OAAO6Y,OAAO,GAAIkT,EAAUD,EAAS,CACrE5S,QAASlZ,OAAO6Y,OAAO,GAAIkT,EAAS7S,QAAS4S,EAAQ5S,SACrDzM,KAAMzM,OAAO6Y,OAAO,GAAIkT,EAAStf,KAAMqf,EAAQrf,QAC5Cqf,EACEP,IACN,IAEIvrB,OAAOC,KAAKsrB,GAAQ7b,KAAI,SAAU3F,GACvC,OAAOwhB,EAAOxhB,QFsGV,OAvCA2O,EAAMwS,iBAAmBA,EAAiBxd,QAAO,SAAUse,GACzD,OAAOA,EAAEzT,WAqJbG,EAAMwS,iBAAiBhrB,SAAQ,SAAUge,GACvC,IAAInb,EAAOmb,EAAMnb,KACbkpB,EAAgB/N,EAAMhF,QACtBA,OAA4B,IAAlB+S,EAA2B,GAAKA,EAC1CnT,EAASoF,EAAMpF,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAIoT,EAAYpT,EAAO,CACrBJ,MAAOA,EACP3V,KAAMA,EACNwH,SAAUA,EACV2O,QAASA,IAKXiS,EAAiB3nB,KAAK0oB,GAFT,kBA7HR3hB,EAASwV,UAOlBoM,YAAa,WACX,IAAIf,EAAJ,CAIA,IAAIgB,EAAkB1T,EAAMC,SACxBW,EAAY8S,EAAgB9S,UAC5BL,EAASmT,EAAgBnT,OAG7B,GAAKqR,GAAiBhR,EAAWL,GAAjC,CASAP,EAAM+D,MAAQ,CACZnD,UAAWwQ,GAAiBxQ,EAAWyB,GAAgB9B,GAAoC,UAA3BP,EAAMQ,QAAQC,UAC9EF,OAAQmB,GAAcnB,IAOxBP,EAAM+N,OAAQ,EACd/N,EAAMhB,UAAYgB,EAAMQ,QAAQxB,UAKhCgB,EAAMwS,iBAAiBhrB,SAAQ,SAAUwrB,GACvC,OAAOhT,EAAM2D,cAAcqP,EAAS3oB,MAAQ/C,OAAO6Y,OAAO,GAAI6S,EAASjf,SAIzE,IAAK,IAAI1H,EAAQ,EAAGA,EAAQ2T,EAAMwS,iBAAiBvrB,OAAQoF,IAUzD,IAAoB,IAAhB2T,EAAM+N,MAAV,CAMA,IAAI4F,EAAwB3T,EAAMwS,iBAAiBnmB,GAC/C7B,EAAKmpB,EAAsBnpB,GAC3BopB,EAAyBD,EAAsBnT,QAC/CoJ,OAAsC,IAA3BgK,EAAoC,GAAKA,EACpDvpB,EAAOspB,EAAsBtpB,KAEf,mBAAPG,IACTwV,EAAQxV,EAAG,CACTwV,MAAOA,EACPQ,QAASoJ,EACTvf,KAAMA,EACNwH,SAAUA,KACNmO,QAjBNA,EAAM+N,OAAQ,EACd1hB,GAAS,KAsBfgb,QCjM2B7c,EDiMV,WACf,OAAO,IAAIqpB,SAAQ,SAAUC,GAC3BjiB,EAAS4hB,cACTK,EAAQ9T,OClMT,WAUL,OATKuS,IACHA,EAAU,IAAIsB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBxB,OAAUve,EACV8f,EAAQtpB,YAKP+nB,ID2LLyB,QAAS,WACPpB,IACAF,GAAc,IAIlB,IAAKd,GAAiBhR,EAAWL,GAK/B,OAAO1O,EAmCT,SAAS+gB,IACPH,EAAiBjrB,SAAQ,SAAUgD,GACjC,OAAOA,OAETioB,EAAmB,GAGrB,OAvCA5gB,EAAS8gB,WAAWnS,GAASuT,MAAK,SAAU/T,IACrC0S,GAAelS,EAAQyT,eAC1BzT,EAAQyT,cAAcjU,MAqCnBnO,GAGJ,IAAIqiB,GAA4BlC,KIzPnCkC,GAA4BlC,GAAgB,CAC9CI,iBAFqB,CAACtL,GAAgBpD,GAAeyQ,GAAeC,MCMlEF,GAA4BlC,GAAgB,CAC9CI,iBAFqB,CAACtL,GAAgBpD,GAAeyQ,GAAeC,GAAahf,GAAQif,GAAM/F,GAAiB3N,GAAOhD,2KpDNvG,+BAEC,YACF,sBACY,2BACP,kBACF,mBACG,4DAQC,kBACN,iBACK,uBAEC,kBACN,iBACK,wBAEE,oBACN,mBACK,0JqDGxB,MAYM2W,GAAiB,IAAIrsB,OAAQ,4BAqB7BssB,GAAgBxqB,IAAU,UAAY,YACtCyqB,GAAmBzqB,IAAU,YAAc,UAC3C0qB,GAAmB1qB,IAAU,aAAe,eAC5C2qB,GAAsB3qB,IAAU,eAAiB,aACjD4qB,GAAkB5qB,IAAU,aAAe,cAC3C6qB,GAAiB7qB,IAAU,cAAgB,aAE3CoN,GAAU,CACd/B,OAAQ,CAAC,EAAG,GACZ2U,SAAU,kBACVnJ,UAAW,SACXiU,QAAS,UACTC,aAAc,KACdC,WAAW,GAGPrd,GAAc,CAClBtC,OAAQ,0BACR2U,SAAU,mBACVnJ,UAAW,0BACXiU,QAAS,SACTC,aAAc,yBACdC,UAAW,oBASb,MAAMC,WAAiB1iB,EACrBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAKomB,QAAU,KACfpmB,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKqmB,MAAQrmB,KAAKsmB,kBAClBtmB,KAAKumB,UAAYvmB,KAAKwmB,gBAKNle,qBAChB,OAAOA,GAGaO,yBACpB,OAAOA,GAGMpN,kBACb,MArFS,WA0FX4J,SACE,OAAOrF,KAAK6O,WAAa7O,KAAK8O,OAAS9O,KAAK+O,OAG9CA,OACE,GAAInV,EAAWoG,KAAK2D,WAAa3D,KAAK6O,SAAS7O,KAAKqmB,OAClD,OAGF,MAAMvmB,EAAgB,CACpBA,cAAeE,KAAK2D,UAKtB,GAFkBrD,EAAamB,QAAQzB,KAAK2D,SAvF5B,mBAuFkD7D,GAEpDiC,iBACZ,OAGF,MAAMmM,EAASiY,GAASM,qBAAqBzmB,KAAK2D,UAE9C3D,KAAKumB,UACP3gB,EAAYC,iBAAiB7F,KAAKqmB,MAAO,SAAU,QAEnDrmB,KAAK0mB,cAAcxY,GAOjB,iBAAkBzW,SAAS2C,kBAC5B8T,EAAOtJ,QA5Fc,gBA6FtB,GAAGuC,UAAU1P,SAASuD,KAAKuM,UACxB5O,QAAQ4V,GAAQjO,EAAaQ,GAAGyN,EAAM,YAAa7T,IAGxDsF,KAAK2D,SAASgjB,QACd3mB,KAAK2D,SAAS2B,aAAa,iBAAiB,GAE5CtF,KAAKqmB,MAAMtsB,UAAUqS,IA5GD,QA6GpBpM,KAAK2D,SAAS5J,UAAUqS,IA7GJ,QA8GpB9L,EAAamB,QAAQzB,KAAK2D,SAnHT,oBAmHgC7D,GAGnDgP,OACE,GAAIlV,EAAWoG,KAAK2D,YAAc3D,KAAK6O,SAAS7O,KAAKqmB,OACnD,OAGF,MAAMvmB,EAAgB,CACpBA,cAAeE,KAAK2D,UAGtB3D,KAAK4mB,cAAc9mB,GAGrB+D,UACM7D,KAAKomB,SACPpmB,KAAKomB,QAAQjB,UAGf7b,MAAMzF,UAGR2U,SACExY,KAAKumB,UAAYvmB,KAAKwmB,gBAClBxmB,KAAKomB,SACPpmB,KAAKomB,QAAQ5N,SAMjBoO,cAAc9mB,GACMQ,EAAamB,QAAQzB,KAAK2D,SAvJ5B,mBAuJkD7D,GACpDiC,mBAMV,iBAAkBtK,SAAS2C,iBAC7B,GAAG+M,UAAU1P,SAASuD,KAAKuM,UACxB5O,QAAQ4V,GAAQjO,EAAaC,IAAIgO,EAAM,YAAa7T,IAGrDsF,KAAKomB,SACPpmB,KAAKomB,QAAQjB,UAGfnlB,KAAKqmB,MAAMtsB,UAAUwJ,OA/JD,QAgKpBvD,KAAK2D,SAAS5J,UAAUwJ,OAhKJ,QAiKpBvD,KAAK2D,SAAS2B,aAAa,gBAAiB,SAC5CM,EAAYE,oBAAoB9F,KAAKqmB,MAAO,UAC5C/lB,EAAamB,QAAQzB,KAAK2D,SA1KR,qBA0KgC7D,IAGpDkK,WAAWzR,GAST,GARAA,EAAS,IACJyH,KAAK0D,YAAY4E,WACjB1C,EAAYI,kBAAkBhG,KAAK2D,aACnCpL,GAGLF,EAnMS,WAmMaE,EAAQyH,KAAK0D,YAAYmF,aAEf,iBAArBtQ,EAAOwZ,YAA2Bha,EAAUQ,EAAOwZ,YACV,mBAA3CxZ,EAAOwZ,UAAUtL,sBAGxB,MAAM,IAAInN,UAzMH,WAyMqBC,cAAP,kGAGvB,OAAOhB,EAGTmuB,cAAcxY,GACZ,QAAsB,IAAX2Y,GACT,MAAM,IAAIvtB,UAAU,gEAGtB,IAAIqiB,EAAmB3b,KAAK2D,SAEG,WAA3B3D,KAAK+J,QAAQgI,UACf4J,EAAmBzN,EACVnW,EAAUiI,KAAK+J,QAAQgI,WAChC4J,EAAmBxjB,EAAW6H,KAAK+J,QAAQgI,WACA,iBAA3B/R,KAAK+J,QAAQgI,YAC7B4J,EAAmB3b,KAAK+J,QAAQgI,WAGlC,MAAMkU,EAAejmB,KAAK8mB,mBACpBC,EAAkBd,EAAanD,UAAU5b,KAAKid,GAA8B,gBAAlBA,EAAS3oB,OAA+C,IAArB2oB,EAASnT,SAE5GhR,KAAKomB,QAAUS,GAAoBlL,EAAkB3b,KAAKqmB,MAAOJ,GAE7Dc,GACFnhB,EAAYC,iBAAiB7F,KAAKqmB,MAAO,SAAU,UAIvDxX,SAAS7X,EAAUgJ,KAAK2D,UACtB,OAAO3M,EAAQ+C,UAAUC,SAnNL,QAsNtBssB,kBACE,OAAOrf,EAAec,KAAK/H,KAAK2D,SAhNd,kBAgNuC,GAG3DqjB,gBACE,MAAMC,EAAiBjnB,KAAK2D,SAASlJ,WAErC,GAAIwsB,EAAeltB,UAAUC,SA3NN,WA4NrB,OAAO8rB,GAGT,GAAImB,EAAeltB,UAAUC,SA9NJ,aA+NvB,OAAO+rB,GAIT,MAAMmB,EAAkF,QAA1ExtB,iBAAiBsG,KAAKqmB,OAAO1sB,iBAAiB,iBAAiBpC,OAE7E,OAAI0vB,EAAeltB,UAAUC,SAvOP,UAwObktB,EAAQvB,GAAmBD,GAG7BwB,EAAQrB,GAAsBD,GAGvCY,gBACE,OAA0D,OAAnDxmB,KAAK2D,SAASiB,QAAS,WAGhCuiB,aACE,MAAM5gB,OAAEA,GAAWvG,KAAK+J,QAExB,MAAsB,iBAAXxD,EACFA,EAAOjP,MAAM,KAAK6Q,IAAI3C,GAAO9I,OAAOoQ,SAAStH,EAAK,KAGrC,mBAAXe,EACF6gB,GAAc7gB,EAAO6gB,EAAYpnB,KAAK2D,UAGxC4C,EAGTugB,mBACE,MAAMO,EAAwB,CAC5BlX,UAAWnQ,KAAKgnB,gBAChBlE,UAAW,CAAC,CACVtnB,KAAM,kBACNmW,QAAS,CACPuJ,SAAUlb,KAAK+J,QAAQmR,WAG3B,CACE1f,KAAM,SACNmW,QAAS,CACPpL,OAAQvG,KAAKmnB,iBAanB,MAP6B,WAAzBnnB,KAAK+J,QAAQic,UACfqB,EAAsBvE,UAAY,CAAC,CACjCtnB,KAAM,cACNwV,SAAS,KAIN,IACFqW,KACsC,mBAA9BrnB,KAAK+J,QAAQkc,aAA8BjmB,KAAK+J,QAAQkc,aAAaoB,GAAyBrnB,KAAK+J,QAAQkc,cAI1HqB,iBAAgB9kB,IAAEA,EAAFxF,OAAOA,IACrB,MAAMuqB,EAAQtgB,EAAeC,KAxRF,8DAwR+BlH,KAAKqmB,OAAOlgB,OAAO3M,GAExE+tB,EAAMnvB,QAMX+E,EAAqBoqB,EAAOvqB,EAtTT,cAsTiBwF,GAAyB+kB,EAAMnwB,SAAS4F,IAAS2pB,QAKjExiB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOihB,GAASthB,oBAAoB7E,KAAMzH,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,SAIQ4L,kBAACjF,GAChB,GAAIA,IA3UmB,IA2UTA,EAAMyG,QAAiD,UAAfzG,EAAMsB,MA9UhD,QA8UoEtB,EAAMsD,KACpF,OAGF,MAAMglB,EAAUvgB,EAAeC,KA7TN,+BA+TzB,IAAK,IAAIlI,EAAI,EAAGC,EAAMuoB,EAAQpvB,OAAQ4G,EAAIC,EAAKD,IAAK,CAClD,MAAMyoB,EAAUtB,GAAS/hB,YAAYojB,EAAQxoB,IAC7C,IAAKyoB,IAAyC,IAA9BA,EAAQ1d,QAAQmc,UAC9B,SAGF,IAAKuB,EAAQ5Y,WACX,SAGF,MAAM/O,EAAgB,CACpBA,cAAe2nB,EAAQ9jB,UAGzB,GAAIzE,EAAO,CACT,MAAMwoB,EAAexoB,EAAMwoB,eACrBC,EAAeD,EAAatwB,SAASqwB,EAAQpB,OACnD,GACEqB,EAAatwB,SAASqwB,EAAQ9jB,WACC,WAA9B8jB,EAAQ1d,QAAQmc,YAA2ByB,GACb,YAA9BF,EAAQ1d,QAAQmc,WAA2ByB,EAE5C,SAIF,GAAIF,EAAQpB,MAAMrsB,SAASkF,EAAMlC,UAA4B,UAAfkC,EAAMsB,MA9W5C,QA8WgEtB,EAAMsD,KAAoB,qCAAqCnJ,KAAK6F,EAAMlC,OAAO2H,UACvJ,SAGiB,UAAfzF,EAAMsB,OACRV,EAAc4E,WAAaxF,GAI/BuoB,EAAQb,cAAc9mB,IAICqE,4BAACnN,GAC1B,OAAOW,EAAuBX,IAAYA,EAAQyD,WAGxB0J,6BAACjF,GAQ3B,GAAI,kBAAkB7F,KAAK6F,EAAMlC,OAAO2H,SAxY1B,UAyYZzF,EAAMsD,KA1YO,WA0YetD,EAAMsD,MAtYjB,cAuYftD,EAAMsD,KAxYO,YAwYmBtD,EAAMsD,KACtCtD,EAAMlC,OAAO4H,QApXC,oBAqXf6gB,GAAepsB,KAAK6F,EAAMsD,KAC3B,OAGF,MAAMolB,EAAW5nB,KAAKjG,UAAUC,SAhYZ,QAkYpB,IAAK4tB,GAnZU,WAmZE1oB,EAAMsD,IACrB,OAMF,GAHAtD,EAAMyD,iBACNzD,EAAM2oB,kBAEFjuB,EAAWoG,MACb,OAGF,MAAM8nB,EAAkB9nB,KAAKyH,QAvYJ,+BAuYoCzH,KAAOiH,EAAeW,KAAK5H,KAvY/D,+BAuY2F,GAC9GgD,EAAWmjB,GAASthB,oBAAoBijB,GAE9C,GAjae,WAiaX5oB,EAAMsD,IAKV,MAnaiB,YAmabtD,EAAMsD,KAlaS,cAkaetD,EAAMsD,KACjColB,GACH5kB,EAAS+L,YAGX/L,EAASskB,gBAAgBpoB,SAItB0oB,GA9aS,UA8aG1oB,EAAMsD,KACrB2jB,GAAS4B,cAdT/kB,EAAS8L,QAyBfxO,EAAaQ,GAAGrJ,SA7agB,+BASH,8BAoa2C0uB,GAAS6B,uBACjF1nB,EAAaQ,GAAGrJ,SA9agB,+BAUV,iBAoa2C0uB,GAAS6B,uBAC1E1nB,EAAaQ,GAAGrJ,SAhbc,6BAgbkB0uB,GAAS4B,YACzDznB,EAAaQ,GAAGrJ,SA/ac,6BA+akB0uB,GAAS4B,YACzDznB,EAAaQ,GAAGrJ,SAlbc,6BAUD,+BAwayC,SAAUyH,GAC9EA,EAAMyD,iBACNwjB,GAASthB,oBAAoB7E,MAAMqF,YAUrCjK,EAAmB+qB,IClenB,MAAM8B,GACJvkB,cACE1D,KAAK2D,SAAWlM,SAASuD,KAG3BktB,WAEE,MAAMC,EAAgB1wB,SAAS2C,gBAAgBwb,YAC/C,OAAOjY,KAAK4N,IAAIxQ,OAAOqtB,WAAaD,GAGtCrZ,OACE,MAAM0D,EAAQxS,KAAKkoB,WACnBloB,KAAKqoB,mBAELroB,KAAKsoB,sBAAsBtoB,KAAK2D,SAAU,eAAgB4kB,GAAmBA,EAAkB/V,GAE/FxS,KAAKsoB,sBApBsB,oDAoBwB,eAAgBC,GAAmBA,EAAkB/V,GACxGxS,KAAKsoB,sBApBuB,cAoBwB,cAAeC,GAAmBA,EAAkB/V,GAG1G6V,mBACEroB,KAAKwoB,sBAAsBxoB,KAAK2D,SAAU,YAC1C3D,KAAK2D,SAAS4L,MAAM4J,SAAW,SAGjCmP,sBAAsBrxB,EAAUwxB,EAAWntB,GACzC,MAAMotB,EAAiB1oB,KAAKkoB,WAW5BloB,KAAK2oB,2BAA2B1xB,EAVHD,IAC3B,GAAIA,IAAYgJ,KAAK2D,UAAY5I,OAAOqtB,WAAapxB,EAAQ4e,YAAc8S,EACzE,OAGF1oB,KAAKwoB,sBAAsBxxB,EAASyxB,GACpC,MAAMF,EAAkBxtB,OAAOrB,iBAAiB1C,GAASyxB,GACzDzxB,EAAQuY,MAAMkZ,GAAgBntB,EAASoB,OAAOC,WAAW4rB,IAA7B,OAMhCrJ,QACElf,KAAK4oB,wBAAwB5oB,KAAK2D,SAAU,YAC5C3D,KAAK4oB,wBAAwB5oB,KAAK2D,SAAU,gBAC5C3D,KAAK4oB,wBA/CsB,oDA+C0B,gBACrD5oB,KAAK4oB,wBA/CuB,cA+C0B,eAGxDJ,sBAAsBxxB,EAASyxB,GAC7B,MAAMI,EAAc7xB,EAAQuY,MAAMkZ,GAC9BI,GACFjjB,EAAYC,iBAAiB7O,EAASyxB,EAAWI,GAIrDD,wBAAwB3xB,EAAUwxB,GAWhCzoB,KAAK2oB,2BAA2B1xB,EAVHD,IAC3B,MAAM8B,EAAQ8M,EAAYU,iBAAiBtP,EAASyxB,QAC/B,IAAV3vB,EACT9B,EAAQuY,MAAMuZ,eAAeL,IAE7B7iB,EAAYE,oBAAoB9O,EAASyxB,GACzCzxB,EAAQuY,MAAMkZ,GAAa3vB,KAOjC6vB,2BAA2B1xB,EAAU8xB,GAC/BhxB,EAAUd,GACZ8xB,EAAS9xB,GAETgQ,EAAeC,KAAKjQ,EAAU+I,KAAK2D,UAAUhL,QAAQowB,GAIzDC,gBACE,OAAOhpB,KAAKkoB,WAAa,GClF7B,MAAM5f,GAAU,CACd2gB,UAAW,iBACXzvB,WAAW,EACX0K,YAAY,EACZglB,YAAa,OACbC,cAAe,MAGXtgB,GAAc,CAClBogB,UAAW,SACXzvB,UAAW,UACX0K,WAAY,UACZglB,YAAa,mBACbC,cAAe,mBAQjB,MAAMC,GACJ1lB,YAAYnL,GACVyH,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKqpB,aAAc,EACnBrpB,KAAK2D,SAAW,KAGlBoL,KAAKzT,GACE0E,KAAK+J,QAAQvQ,WAKlBwG,KAAKspB,UAEDtpB,KAAK+J,QAAQ7F,YACfvJ,EAAOqF,KAAKupB,eAGdvpB,KAAKupB,cAAcxvB,UAAUqS,IAvBT,QAyBpBpM,KAAKwpB,kBAAkB,KACrBttB,EAAQZ,MAbRY,EAAQZ,GAiBZwT,KAAKxT,GACE0E,KAAK+J,QAAQvQ,WAKlBwG,KAAKupB,cAAcxvB,UAAUwJ,OApCT,QAsCpBvD,KAAKwpB,kBAAkB,KACrBxpB,KAAK6D,UACL3H,EAAQZ,MARRY,EAAQZ,GAcZiuB,cACE,IAAKvpB,KAAK2D,SAAU,CAClB,MAAM8lB,EAAWhyB,SAASiyB,cAAc,OACxCD,EAASR,UAAYjpB,KAAK+J,QAAQkf,UAC9BjpB,KAAK+J,QAAQ7F,YACfulB,EAAS1vB,UAAUqS,IApDH,QAuDlBpM,KAAK2D,SAAW8lB,EAGlB,OAAOzpB,KAAK2D,SAGdqG,WAAWzR,GAST,OARAA,EAAS,IACJ+P,MACmB,iBAAX/P,EAAsBA,EAAS,KAIrC2wB,YAAc/wB,EAAWI,EAAO2wB,aACvC7wB,EAtES,WAsEaE,EAAQsQ,IACvBtQ,EAGT+wB,UACMtpB,KAAKqpB,cAITrpB,KAAK+J,QAAQmf,YAAYS,OAAO3pB,KAAKupB,eAErCjpB,EAAaQ,GAAGd,KAAKupB,cA7EA,wBA6EgC,KACnDrtB,EAAQ8D,KAAK+J,QAAQof,iBAGvBnpB,KAAKqpB,aAAc,GAGrBxlB,UACO7D,KAAKqpB,cAIV/oB,EAAaC,IAAIP,KAAK2D,SAzFD,yBA2FrB3D,KAAK2D,SAASJ,SACdvD,KAAKqpB,aAAc,GAGrBG,kBAAkBluB,GAChBa,EAAuBb,EAAU0E,KAAKupB,cAAevpB,KAAK+J,QAAQ7F,aClHtE,MAAMoE,GAAU,CACdshB,YAAa,KACbC,WAAW,GAGPhhB,GAAc,CAClB+gB,YAAa,UACbC,UAAW,WAab,MAAMC,GACJpmB,YAAYnL,GACVyH,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAK+pB,WAAY,EACjB/pB,KAAKgqB,qBAAuB,KAG9BC,WACE,MAAML,YAAEA,EAAFC,UAAeA,GAAc7pB,KAAK+J,QAEpC/J,KAAK+pB,YAILF,GACFD,EAAYjD,QAGdrmB,EAAaC,IAAI9I,SA1BF,iBA2Bf6I,EAAaQ,GAAGrJ,SA1BG,uBA0BsByH,GAASc,KAAKkqB,eAAehrB,IACtEoB,EAAaQ,GAAGrJ,SA1BO,2BA0BsByH,GAASc,KAAKmqB,eAAejrB,IAE1Ec,KAAK+pB,WAAY,GAGnBK,aACOpqB,KAAK+pB,YAIV/pB,KAAK+pB,WAAY,EACjBzpB,EAAaC,IAAI9I,SAvCF,kBA4CjByyB,eAAehrB,GACb,MAAMlC,OAAEA,GAAWkC,GACb0qB,YAAEA,GAAgB5pB,KAAK+J,QAE7B,GACE/M,IAAWvF,UACXuF,IAAW4sB,GACXA,EAAY5vB,SAASgD,GAErB,OAGF,MAAMoU,EAAWnK,EAAegB,kBAAkB2hB,GAE1B,IAApBxY,EAAShZ,OACXwxB,EAAYjD,QArDO,aAsDV3mB,KAAKgqB,qBACd5Y,EAASA,EAAShZ,OAAS,GAAGuuB,QAE9BvV,EAAS,GAAGuV,QAIhBwD,eAAejrB,GA/DD,QAgERA,EAAMsD,MAIVxC,KAAKgqB,qBAAuB9qB,EAAMmrB,SAlEb,WADD,WAsEtBrgB,WAAWzR,GAMT,OALAA,EAAS,IACJ+P,MACmB,iBAAX/P,EAAsBA,EAAS,IAE5CF,EAlFS,YAkFaE,EAAQsQ,IACvBtQ,GC1EX,MAMM+P,GAAU,CACdmhB,UAAU,EACVjhB,UAAU,EACVme,OAAO,GAGH9d,GAAc,CAClB4gB,SAAU,mBACVjhB,SAAU,UACVme,MAAO,WA8BT,MAAM2D,WAAc7mB,EAClBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKuqB,QAAUtjB,EAAeK,QAfV,gBAemCtH,KAAK2D,UAC5D3D,KAAKwqB,UAAYxqB,KAAKyqB,sBACtBzqB,KAAK0qB,WAAa1qB,KAAK2qB,uBACvB3qB,KAAK6O,UAAW,EAChB7O,KAAK4qB,sBAAuB,EAC5B5qB,KAAKoO,kBAAmB,EACxBpO,KAAK6qB,WAAa,IAAI5C,GAKN3f,qBAChB,OAAOA,GAGM7M,kBACb,MAlES,QAuEX4J,OAAOvF,GACL,OAAOE,KAAK6O,SAAW7O,KAAK8O,OAAS9O,KAAK+O,KAAKjP,GAGjDiP,KAAKjP,GACCE,KAAK6O,UAAY7O,KAAKoO,kBAIR9N,EAAamB,QAAQzB,KAAK2D,SA3D5B,gBA2DkD,CAChE7D,cAAAA,IAGYiC,mBAId/B,KAAK6O,UAAW,EAEZ7O,KAAK8qB,gBACP9qB,KAAKoO,kBAAmB,GAG1BpO,KAAK6qB,WAAW/b,OAEhBrX,SAASuD,KAAKjB,UAAUqS,IAlEJ,cAoEpBpM,KAAK+qB,gBAEL/qB,KAAKgrB,kBACLhrB,KAAKirB,kBAEL3qB,EAAaQ,GAAGd,KAAKuqB,QA5EQ,6BA4E0B,KACrDjqB,EAAaS,IAAIf,KAAK2D,SA9EG,2BA8E8BzE,IACjDA,EAAMlC,SAAWgD,KAAK2D,WACxB3D,KAAK4qB,sBAAuB,OAKlC5qB,KAAKkrB,cAAc,IAAMlrB,KAAKmrB,aAAarrB,KAG7CgP,OACE,IAAK9O,KAAK6O,UAAY7O,KAAKoO,iBACzB,OAKF,GAFkB9N,EAAamB,QAAQzB,KAAK2D,SArG5B,iBAuGF5B,iBACZ,OAGF/B,KAAK6O,UAAW,EAChB,MAAM3K,EAAalE,KAAK8qB,cAEpB5mB,IACFlE,KAAKoO,kBAAmB,GAG1BpO,KAAKgrB,kBACLhrB,KAAKirB,kBAELjrB,KAAK0qB,WAAWN,aAEhBpqB,KAAK2D,SAAS5J,UAAUwJ,OAzGJ,QA2GpBjD,EAAaC,IAAIP,KAAK2D,SAnHG,0BAoHzBrD,EAAaC,IAAIP,KAAKuqB,QAjHO,8BAmH7BvqB,KAAKiE,eAAe,IAAMjE,KAAKorB,aAAcprB,KAAK2D,SAAUO,GAG9DL,UACE,CAAC9I,OAAQiF,KAAKuqB,SACX5xB,QAAQ0yB,GAAe/qB,EAAaC,IAAI8qB,EAjJ5B,cAmJfrrB,KAAKwqB,UAAU3mB,UACf7D,KAAK0qB,WAAWN,aAChB9gB,MAAMzF,UAGRynB,eACEtrB,KAAK+qB,gBAKPN,sBACE,OAAO,IAAIrB,GAAS,CAClB5vB,UAAWqH,QAAQb,KAAK+J,QAAQ0f,UAChCvlB,WAAYlE,KAAK8qB,gBAIrBH,uBACE,OAAO,IAAIb,GAAU,CACnBF,YAAa5pB,KAAK2D,WAItBqG,WAAWzR,GAOT,OANAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EAnLS,QAmLaE,EAAQsQ,IACvBtQ,EAGT4yB,aAAarrB,GACX,MAAMoE,EAAalE,KAAK8qB,cAClBS,EAAYtkB,EAAeK,QArJT,cAqJsCtH,KAAKuqB,SAE9DvqB,KAAK2D,SAASlJ,YAAcuF,KAAK2D,SAASlJ,WAAWvC,WAAa2B,KAAKC,cAE1ErC,SAASuD,KAAK2uB,OAAO3pB,KAAK2D,UAG5B3D,KAAK2D,SAAS4L,MAAMyW,QAAU,QAC9BhmB,KAAK2D,SAASoC,gBAAgB,eAC9B/F,KAAK2D,SAAS2B,aAAa,cAAc,GACzCtF,KAAK2D,SAAS2B,aAAa,OAAQ,UACnCtF,KAAK2D,SAASoV,UAAY,EAEtBwS,IACFA,EAAUxS,UAAY,GAGpB7U,GACFvJ,EAAOqF,KAAK2D,UAGd3D,KAAK2D,SAAS5J,UAAUqS,IA9KJ,QA2LpBpM,KAAKiE,eAXsB,KACrBjE,KAAK+J,QAAQ4c,OACf3mB,KAAK0qB,WAAWT,WAGlBjqB,KAAKoO,kBAAmB,EACxB9N,EAAamB,QAAQzB,KAAK2D,SAhMX,iBAgMkC,CAC/C7D,cAAAA,KAIoCE,KAAKuqB,QAASrmB,GAGxD8mB,kBACMhrB,KAAK6O,SACPvO,EAAaQ,GAAGd,KAAK2D,SAvMI,2BAuM6BzE,IAChDc,KAAK+J,QAAQvB,UA7NN,WA6NkBtJ,EAAMsD,KACjCtD,EAAMyD,iBACN3C,KAAK8O,QACK9O,KAAK+J,QAAQvB,UAhOd,WAgO0BtJ,EAAMsD,KACzCxC,KAAKwrB,+BAITlrB,EAAaC,IAAIP,KAAK2D,SAhNG,4BAoN7BsnB,kBACMjrB,KAAK6O,SACPvO,EAAaQ,GAAG/F,OAxNA,kBAwNsB,IAAMiF,KAAK+qB,iBAEjDzqB,EAAaC,IAAIxF,OA1ND,mBA8NpBqwB,aACEprB,KAAK2D,SAAS4L,MAAMyW,QAAU,OAC9BhmB,KAAK2D,SAAS2B,aAAa,eAAe,GAC1CtF,KAAK2D,SAASoC,gBAAgB,cAC9B/F,KAAK2D,SAASoC,gBAAgB,QAC9B/F,KAAKoO,kBAAmB,EACxBpO,KAAKwqB,UAAU1b,KAAK,KAClBrX,SAASuD,KAAKjB,UAAUwJ,OA9NN,cA+NlBvD,KAAKyrB,oBACLzrB,KAAK6qB,WAAW3L,QAChB5e,EAAamB,QAAQzB,KAAK2D,SA3OV,qBA+OpBunB,cAAc5vB,GACZgF,EAAaQ,GAAGd,KAAK2D,SA5OI,yBA4O2BzE,IAC9Cc,KAAK4qB,qBACP5qB,KAAK4qB,sBAAuB,EAI1B1rB,EAAMlC,SAAWkC,EAAMwsB,iBAIG,IAA1B1rB,KAAK+J,QAAQ0f,SACfzpB,KAAK8O,OAC8B,WAA1B9O,KAAK+J,QAAQ0f,UACtBzpB,KAAKwrB,gCAITxrB,KAAKwqB,UAAUzb,KAAKzT,GAGtBwvB,cACE,OAAO9qB,KAAK2D,SAAS5J,UAAUC,SA1PX,QA6PtBwxB,6BAEE,GADkBlrB,EAAamB,QAAQzB,KAAK2D,SA1QlB,0BA2QZ5B,iBACZ,OAGF,MAAMhI,UAAEA,EAAFugB,aAAaA,EAAb/K,MAA2BA,GAAUvP,KAAK2D,SAC1CgoB,EAAqBrR,EAAe7iB,SAAS2C,gBAAgBub,cAG7DgW,GAA0C,WAApBpc,EAAM8J,WAA2Btf,EAAUC,SArQjD,kBAyQjB2xB,IACHpc,EAAM8J,UAAY,UAGpBtf,EAAUqS,IA7QY,gBA8QtBpM,KAAKiE,eAAe,KAClBlK,EAAUwJ,OA/QU,gBAgRfooB,GACH3rB,KAAKiE,eAAe,KAClBsL,EAAM8J,UAAY,IACjBrZ,KAAKuqB,UAETvqB,KAAKuqB,SAERvqB,KAAK2D,SAASgjB,SAOhBoE,gBACE,MAAMY,EAAqB3rB,KAAK2D,SAAS2W,aAAe7iB,SAAS2C,gBAAgBub,aAC3E+S,EAAiB1oB,KAAK6qB,WAAW3C,WACjC0D,EAAoBlD,EAAiB,IAErCkD,GAAqBD,IAAuBzwB,KAAa0wB,IAAsBD,GAAsBzwB,OACzG8E,KAAK2D,SAAS4L,MAAMsc,YAAiBnD,EAAF,OAGhCkD,IAAsBD,IAAuBzwB,MAAc0wB,GAAqBD,GAAsBzwB,OACzG8E,KAAK2D,SAAS4L,MAAMuc,aAAkBpD,EAAF,MAIxC+C,oBACEzrB,KAAK2D,SAAS4L,MAAMsc,YAAc,GAClC7rB,KAAK2D,SAAS4L,MAAMuc,aAAe,GAKf3nB,uBAAC5L,EAAQuH,GAC7B,OAAOE,KAAKiF,MAAK,WACf,MAAMC,EAAOolB,GAAMzlB,oBAAoB7E,KAAMzH,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQuH,QAWnBQ,EAAaQ,GAAGrJ,SA/Uc,0BASD,4BAsUyC,SAAUyH,GAC9E,MAAMlC,EAASrF,EAAuBqI,MAElC,CAAC,IAAK,QAAQ5I,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGRrC,EAAaS,IAAI/D,EA7VC,gBA6VmB+uB,IAC/BA,EAAUhqB,kBAKdzB,EAAaS,IAAI/D,EApWC,kBAoWqB,KACjCxD,EAAUwG,OACZA,KAAK2mB,YAKE2D,GAAMzlB,oBAAoB7H,GAElCqI,OAAOrF,SAGduE,EAAqB+lB,IASrBlvB,EAAmBkvB,IC9YnB,MAOMhiB,GAAU,CACdmhB,UAAU,EACVjhB,UAAU,EACV2P,QAAQ,GAGJtP,GAAc,CAClB4gB,SAAU,UACVjhB,SAAU,UACV2P,OAAQ,WAsBV,MAAM6T,WAAkBvoB,EACtBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAK6O,UAAW,EAChB7O,KAAKwqB,UAAYxqB,KAAKyqB,sBACtBzqB,KAAK0qB,WAAa1qB,KAAK2qB,uBACvB3qB,KAAKuK,qBAKQ9O,kBACb,MApDS,YAuDO6M,qBAChB,OAAOA,GAKTjD,OAAOvF,GACL,OAAOE,KAAK6O,SAAW7O,KAAK8O,OAAS9O,KAAK+O,KAAKjP,GAGjDiP,KAAKjP,GACCE,KAAK6O,UAISvO,EAAamB,QAAQzB,KAAK2D,SA/C5B,oBA+CkD,CAAE7D,cAAAA,IAEtDiC,mBAId/B,KAAK6O,UAAW,EAChB7O,KAAK2D,SAAS4L,MAAM0c,WAAa,UAEjCjsB,KAAKwqB,UAAUzb,OAEV/O,KAAK+J,QAAQoO,SAChB,IAAI8P,IAAkBnZ,OAGxB9O,KAAK2D,SAASoC,gBAAgB,eAC9B/F,KAAK2D,SAAS2B,aAAa,cAAc,GACzCtF,KAAK2D,SAAS2B,aAAa,OAAQ,UACnCtF,KAAK2D,SAAS5J,UAAUqS,IArEJ,QA+EpBpM,KAAKiE,eARoB,KAClBjE,KAAK+J,QAAQoO,QAChBnY,KAAK0qB,WAAWT,WAGlB3pB,EAAamB,QAAQzB,KAAK2D,SAvEX,qBAuEkC,CAAE7D,cAAAA,KAGfE,KAAK2D,UAAU,IAGvDmL,OACO9O,KAAK6O,WAIQvO,EAAamB,QAAQzB,KAAK2D,SAjF5B,qBAmFF5B,mBAId/B,KAAK0qB,WAAWN,aAChBpqB,KAAK2D,SAASuoB,OACdlsB,KAAK6O,UAAW,EAChB7O,KAAK2D,SAAS5J,UAAUwJ,OAhGJ,QAiGpBvD,KAAKwqB,UAAU1b,OAef9O,KAAKiE,eAboB,KACvBjE,KAAK2D,SAAS2B,aAAa,eAAe,GAC1CtF,KAAK2D,SAASoC,gBAAgB,cAC9B/F,KAAK2D,SAASoC,gBAAgB,QAC9B/F,KAAK2D,SAAS4L,MAAM0c,WAAa,SAE5BjsB,KAAK+J,QAAQoO,SAChB,IAAI8P,IAAkB/I,QAGxB5e,EAAamB,QAAQzB,KAAK2D,SAtGV,wBAyGoB3D,KAAK2D,UAAU,KAGvDE,UACE7D,KAAKwqB,UAAU3mB,UACf7D,KAAK0qB,WAAWN,aAChB9gB,MAAMzF,UAKRmG,WAAWzR,GAOT,OANAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EApJS,YAoJaE,EAAQsQ,IACvBtQ,EAGTkyB,sBACE,OAAO,IAAIrB,GAAS,CAClBH,UAtIsB,qBAuItBzvB,UAAWwG,KAAK+J,QAAQ0f,SACxBvlB,YAAY,EACZglB,YAAalpB,KAAK2D,SAASlJ,WAC3B0uB,cAAe,IAAMnpB,KAAK8O,SAI9B6b,uBACE,OAAO,IAAIb,GAAU,CACnBF,YAAa5pB,KAAK2D,WAItB4G,qBACEjK,EAAaQ,GAAGd,KAAK2D,SA7IM,+BA6I2BzE,IAChDc,KAAK+J,QAAQvB,UArKJ,WAqKgBtJ,EAAMsD,KACjCxC,KAAK8O,SAOW3K,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO8mB,GAAUnnB,oBAAoB7E,KAAMzH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqB4M,IAAjBD,EAAK3M,IAAyBA,EAAOlB,WAAW,MAAmB,gBAAXkB,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,WAWnBM,EAAaQ,GAAGrJ,SA9Kc,8BAGD,gCA2KyC,SAAUyH,GAC9E,MAAMlC,EAASrF,EAAuBqI,MAMtC,GAJI,CAAC,IAAK,QAAQ5I,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,MACb,OAGFM,EAAaS,IAAI/D,EA1LG,sBA0LmB,KAEjCxD,EAAUwG,OACZA,KAAK2mB,UAKT,MAAMwF,EAAellB,EAAeK,QAvMhB,mBAwMhB6kB,GAAgBA,IAAiBnvB,GACnCgvB,GAAU5nB,YAAY+nB,GAAcrd,OAGzBkd,GAAUnnB,oBAAoB7H,GACtCqI,OAAOrF,SAGdM,EAAaQ,GAAG/F,OAjOa,6BAiOgB,IAC3CkM,EAAeC,KAjNK,mBAiNevO,QAAQ0P,GAAM2jB,GAAUnnB,oBAAoBwD,GAAI0G,SAGrFxK,EAAqBynB,IAOrB5wB,EAAmB4wB,ICtQnB,MAAMI,GAAW,IAAI5tB,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI6tB,GAAmB,6DAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKjc,SAASpX,cAE/B,GAAIszB,EAAqBr1B,SAASs1B,GAChC,OAAIN,GAAS1sB,IAAIgtB,IACR7rB,QAAQwrB,GAAiBhzB,KAAKmzB,EAAKG,YAAcL,GAAiBjzB,KAAKmzB,EAAKG,YAMvF,MAAMC,EAASH,EAAqBtmB,OAAO0mB,GAAaA,aAAqBzzB,QAG7E,IAAK,IAAI4F,EAAI,EAAGC,EAAM2tB,EAAOx0B,OAAQ4G,EAAIC,EAAKD,IAC5C,GAAI4tB,EAAO5tB,GAAG3F,KAAKqzB,GACjB,OAAO,EAIX,OAAO,GAqCF,SAASI,GAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW30B,OACd,OAAO20B,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAInyB,OAAOoyB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgB50B,OAAOC,KAAKs0B,GAC5B5b,EAAW,GAAGjK,UAAU+lB,EAAgBlyB,KAAKqF,iBAAiB,MAEpE,IAAK,IAAIrB,EAAI,EAAGC,EAAMmS,EAAShZ,OAAQ4G,EAAIC,EAAKD,IAAK,CACnD,MAAMqJ,EAAK+I,EAASpS,GACdsuB,EAASjlB,EAAGkI,SAASpX,cAE3B,IAAKk0B,EAAcj2B,SAASk2B,GAAS,CACnCjlB,EAAG9E,SAEH,SAGF,MAAMgqB,EAAgB,GAAGpmB,UAAUkB,EAAGpC,YAChCunB,EAAoB,GAAGrmB,OAAO6lB,EAAU,MAAQ,GAAIA,EAAUM,IAAW,IAE/EC,EAAc50B,QAAQ6zB,IACfD,GAAiBC,EAAMgB,IAC1BnlB,EAAGtC,gBAAgBymB,EAAKjc,YAK9B,OAAO2c,EAAgBlyB,KAAKyyB,UC7F9B,MAIMC,GAAwB,IAAIlvB,IAAI,CAAC,WAAY,YAAa,eAE1DqK,GAAc,CAClB8kB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPpsB,QAAS,SACTqsB,MAAO,kBACP/T,KAAM,UACN9iB,SAAU,mBACVkZ,UAAW,oBACX5J,OAAQ,0BACR2I,UAAW,2BACX4O,mBAAoB,QACpB5C,SAAU,mBACV6S,YAAa,oBACbC,SAAU,UACVf,WAAY,kBACZD,UAAW,SACX/G,aAAc,0BAGVgI,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOlzB,IAAU,OAAS,QAC1BmzB,OAAQ,SACRC,KAAMpzB,IAAU,QAAU,QAGtBoN,GAAU,CACdqlB,WAAW,EACXC,SAAU,+GAIVnsB,QAAS,cACTosB,MAAO,GACPC,MAAO,EACP/T,MAAM,EACN9iB,UAAU,EACVkZ,UAAW,MACX5J,OAAQ,CAAC,EAAG,GACZ2I,WAAW,EACX4O,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C5C,SAAU,kBACV6S,YAAa,GACbC,UAAU,EACVf,WAAY,KACZD,UD5B8B,CAE9BuB,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BnR,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BoR,KAAM,GACNnR,EAAG,GACHoR,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJpwB,EAAG,GACHqwB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,ICFJhK,aAAc,MAGVnuB,GAAQ,CACZo4B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBA0Bf,MAAMC,WAAgBntB,EACpBC,YAAY1M,EAASuB,GACnB,QAAsB,IAAXsuB,GACT,MAAM,IAAIvtB,UAAU,+DAGtBgQ,MAAMtS,GAGNgJ,KAAK6wB,YAAa,EAClB7wB,KAAK8wB,SAAW,EAChB9wB,KAAK+wB,YAAc,GACnB/wB,KAAKgxB,eAAiB,GACtBhxB,KAAKomB,QAAU,KAGfpmB,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKixB,IAAM,KAEXjxB,KAAKkxB,gBAKW5oB,qBAChB,OAAOA,GAGM7M,kBACb,MA1HS,UA6HK3D,mBACd,OAAOA,GAGa+Q,yBACpB,OAAOA,GAKTsoB,SACEnxB,KAAK6wB,YAAa,EAGpBO,UACEpxB,KAAK6wB,YAAa,EAGpBQ,gBACErxB,KAAK6wB,YAAc7wB,KAAK6wB,WAG1BxrB,OAAOnG,GACL,GAAKc,KAAK6wB,WAIV,GAAI3xB,EAAO,CACT,MAAMuoB,EAAUznB,KAAKsxB,6BAA6BpyB,GAElDuoB,EAAQuJ,eAAeO,OAAS9J,EAAQuJ,eAAeO,MAEnD9J,EAAQ+J,uBACV/J,EAAQgK,OAAO,KAAMhK,GAErBA,EAAQiK,OAAO,KAAMjK,OAElB,CACL,GAAIznB,KAAK2xB,gBAAgB53B,UAAUC,SA3FjB,QA6FhB,YADAgG,KAAK0xB,OAAO,KAAM1xB,MAIpBA,KAAKyxB,OAAO,KAAMzxB,OAItB6D,UACEoI,aAAajM,KAAK8wB,UAElBxwB,EAAaC,IAAIP,KAAK2D,SAASiB,QAjGX,UAEC,gBA+FqD5E,KAAK4xB,mBAE3E5xB,KAAKixB,KACPjxB,KAAKixB,IAAI1tB,SAGPvD,KAAKomB,SACPpmB,KAAKomB,QAAQjB,UAGf7b,MAAMzF,UAGRkL,OACE,GAAoC,SAAhC/O,KAAK2D,SAAS4L,MAAMyW,QACtB,MAAM,IAAI1hB,MAAM,uCAGlB,IAAMtE,KAAK6xB,kBAAmB7xB,KAAK6wB,WACjC,OAGF,MAAM9E,EAAYzrB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMs4B,MACvE0B,EAAa33B,EAAe6F,KAAK2D,UACjCouB,EAA4B,OAAfD,EACjB9xB,KAAK2D,SAAS+M,cAActW,gBAAgBJ,SAASgG,KAAK2D,UAC1DmuB,EAAW93B,SAASgG,KAAK2D,UAE3B,GAAIooB,EAAUhqB,mBAAqBgwB,EACjC,OAGF,MAAMd,EAAMjxB,KAAK2xB,gBACXK,EvEtNKC,CAAAA,IACb,GACEA,GAAUt0B,KAAKu0B,MArBH,IAqBSv0B,KAAKw0B,gBACnB16B,SAAS26B,eAAeH,IAEjC,OAAOA,GuEiNSI,CAAOryB,KAAK0D,YAAYjI,MAEtCw1B,EAAI3rB,aAAa,KAAM0sB,GACvBhyB,KAAK2D,SAAS2B,aAAa,mBAAoB0sB,GAE3ChyB,KAAK+J,QAAQ4jB,WACfsD,EAAIl3B,UAAUqS,IAhJI,QAmJpB,MAAM+D,EAA8C,mBAA3BnQ,KAAK+J,QAAQoG,UACpCnQ,KAAK+J,QAAQoG,UAAUlX,KAAK+G,KAAMixB,EAAKjxB,KAAK2D,UAC5C3D,KAAK+J,QAAQoG,UAETmiB,EAAatyB,KAAKuyB,eAAepiB,GACvCnQ,KAAKwyB,oBAAoBF,GAEzB,MAAMpjB,UAAEA,GAAclP,KAAK+J,QAC3BjH,EAAKC,IAAIkuB,EAAKjxB,KAAK0D,YAAYE,SAAU5D,MAEpCA,KAAK2D,SAAS+M,cAActW,gBAAgBJ,SAASgG,KAAKixB,OAC7D/hB,EAAUya,OAAOsH,GACjB3wB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMw4B,WAGzDtwB,KAAKomB,QACPpmB,KAAKomB,QAAQ5N,SAEbxY,KAAKomB,QAAUS,GAAoB7mB,KAAK2D,SAAUstB,EAAKjxB,KAAK8mB,iBAAiBwL,IAG/ErB,EAAIl3B,UAAUqS,IAtKM,QAwKpB,MAAM2hB,EAAc/tB,KAAKyyB,yBAAyBzyB,KAAK+J,QAAQgkB,aAC3DA,GACFkD,EAAIl3B,UAAUqS,OAAO2hB,EAAYz2B,MAAM,MAOrC,iBAAkBG,SAAS2C,iBAC7B,GAAG+M,UAAU1P,SAASuD,KAAKuM,UAAU5O,QAAQ3B,IAC3CsJ,EAAaQ,GAAG9J,EAAS,YAAa0D,KAI1C,MAWMwJ,EAAalE,KAAKixB,IAAIl3B,UAAUC,SApMlB,QAqMpBgG,KAAKiE,eAZY,KACf,MAAMyuB,EAAiB1yB,KAAK+wB,YAE5B/wB,KAAK+wB,YAAc,KACnBzwB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMu4B,OAxLzC,QA0LdqC,GACF1yB,KAAK0xB,OAAO,KAAM1xB,OAKQA,KAAKixB,IAAK/sB,GAG1C4K,OACE,IAAK9O,KAAKomB,QACR,OAGF,MAAM6K,EAAMjxB,KAAK2xB,gBAqBjB,GADkBrxB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMo4B,MAC/DnuB,iBACZ,OAGFkvB,EAAIl3B,UAAUwJ,OApOM,QAwOhB,iBAAkB9L,SAAS2C,iBAC7B,GAAG+M,UAAU1P,SAASuD,KAAKuM,UACxB5O,QAAQ3B,GAAWsJ,EAAaC,IAAIvJ,EAAS,YAAa0D,IAG/DsF,KAAKgxB,eAAL,OAAqC,EACrChxB,KAAKgxB,eAAL,OAAqC,EACrChxB,KAAKgxB,eAAL,OAAqC,EAErC,MAAM9sB,EAAalE,KAAKixB,IAAIl3B,UAAUC,SAnPlB,QAoPpBgG,KAAKiE,eAtCY,KACXjE,KAAKwxB,yBA3MU,SA+MfxxB,KAAK+wB,aACPE,EAAI1tB,SAGNvD,KAAK2yB,iBACL3yB,KAAK2D,SAASoC,gBAAgB,oBAC9BzF,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMq4B,QAEvDnwB,KAAKomB,UACPpmB,KAAKomB,QAAQjB,UACbnlB,KAAKomB,QAAU,QAuBWpmB,KAAKixB,IAAK/sB,GACxClE,KAAK+wB,YAAc,GAGrBvY,SACuB,OAAjBxY,KAAKomB,SACPpmB,KAAKomB,QAAQ5N,SAMjBqZ,gBACE,OAAOhxB,QAAQb,KAAK4yB,YAGtBjB,gBACE,GAAI3xB,KAAKixB,IACP,OAAOjxB,KAAKixB,IAGd,MAAMj6B,EAAUS,SAASiyB,cAAc,OACvC1yB,EAAQy2B,UAAYztB,KAAK+J,QAAQ6jB,SAEjC,MAAMqD,EAAMj6B,EAAQuQ,SAAS,GAK7B,OAJAvH,KAAK6yB,WAAW5B,GAChBA,EAAIl3B,UAAUwJ,OA9QM,OAEA,QA8QpBvD,KAAKixB,IAAMA,EACJjxB,KAAKixB,IAGd4B,WAAW5B,GACTjxB,KAAK8yB,uBAAuB7B,EAAKjxB,KAAK4yB,WA9QX,kBAiR7BE,uBAAuBlF,EAAUmF,EAAS97B,GACxC,MAAM+7B,EAAkB/rB,EAAeK,QAAQrQ,EAAU22B,GAEpDmF,IAAWC,EAMhBhzB,KAAKizB,kBAAkBD,EAAiBD,GALtCC,EAAgBzvB,SAQpB0vB,kBAAkBj8B,EAAS+7B,GACzB,GAAgB,OAAZ/7B,EAIJ,OAAIe,EAAUg7B,IACZA,EAAU56B,EAAW46B,QAGjB/yB,KAAK+J,QAAQgQ,KACXgZ,EAAQt4B,aAAezD,IACzBA,EAAQy2B,UAAY,GACpBz2B,EAAQ2yB,OAAOoJ,IAGjB/7B,EAAQk8B,YAAcH,EAAQG,mBAM9BlzB,KAAK+J,QAAQgQ,MACX/Z,KAAK+J,QAAQikB,WACf+E,EAAUjG,GAAaiG,EAAS/yB,KAAK+J,QAAQijB,UAAWhtB,KAAK+J,QAAQkjB,aAGvEj2B,EAAQy2B,UAAYsF,GAEpB/7B,EAAQk8B,YAAcH,GAI1BH,WACE,MAAM/E,EAAQ7tB,KAAK2D,SAASzM,aAAa,2BAA6B8I,KAAK+J,QAAQ8jB,MAEnF,OAAO7tB,KAAKyyB,yBAAyB5E,GAGvCsF,iBAAiBb,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKThB,6BAA6BpyB,EAAOuoB,GAClC,OAAOA,GAAWznB,KAAK0D,YAAYmB,oBAAoB3F,EAAMa,eAAgBC,KAAKozB,sBAGpFjM,aACE,MAAM5gB,OAAEA,GAAWvG,KAAK+J,QAExB,MAAsB,iBAAXxD,EACFA,EAAOjP,MAAM,KAAK6Q,IAAI3C,GAAO9I,OAAOoQ,SAAStH,EAAK,KAGrC,mBAAXe,EACF6gB,GAAc7gB,EAAO6gB,EAAYpnB,KAAK2D,UAGxC4C,EAGTksB,yBAAyBM,GACvB,MAA0B,mBAAZA,EAAyBA,EAAQ95B,KAAK+G,KAAK2D,UAAYovB,EAGvEjM,iBAAiBwL,GACf,MAAMjL,EAAwB,CAC5BlX,UAAWmiB,EACXxP,UAAW,CACT,CACEtnB,KAAM,OACNmW,QAAS,CACPmM,mBAAoB9d,KAAK+J,QAAQ+T,qBAGrC,CACEtiB,KAAM,SACNmW,QAAS,CACPpL,OAAQvG,KAAKmnB,eAGjB,CACE3rB,KAAM,kBACNmW,QAAS,CACPuJ,SAAUlb,KAAK+J,QAAQmR,WAG3B,CACE1f,KAAM,QACNmW,QAAS,CACP3a,QAAU,IAAGgJ,KAAK0D,YAAYjI,eAGlC,CACED,KAAM,WACNwV,SAAS,EACTC,MAAO,aACPtV,GAAIuJ,GAAQlF,KAAKqzB,6BAA6BnuB,KAGlDkgB,cAAelgB,IACTA,EAAKyM,QAAQxB,YAAcjL,EAAKiL,WAClCnQ,KAAKqzB,6BAA6BnuB,KAKxC,MAAO,IACFmiB,KACsC,mBAA9BrnB,KAAK+J,QAAQkc,aAA8BjmB,KAAK+J,QAAQkc,aAAaoB,GAAyBrnB,KAAK+J,QAAQkc,cAI1HuM,oBAAoBF,GAClBtyB,KAAK2xB,gBAAgB53B,UAAUqS,IAAK,GAAEpM,KAAKszB,0BAA0BtzB,KAAKmzB,iBAAiBb,MAG7FC,eAAepiB,GACb,OAAO8d,GAAc9d,EAAU5W,eAGjC23B,gBACmBlxB,KAAK+J,QAAQtI,QAAQnK,MAAM,KAEnCqB,QAAQ8I,IACf,GAAgB,UAAZA,EACFnB,EAAaQ,GAAGd,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMy4B,MAAOvwB,KAAK+J,QAAQ9S,SAAUiI,GAASc,KAAKqF,OAAOnG,SACpG,GA7ZU,WA6ZNuC,EAA4B,CACrC,MAAM8xB,EAjaQ,UAiaE9xB,EACdzB,KAAK0D,YAAY5L,MAAM44B,WACvB1wB,KAAK0D,YAAY5L,MAAM04B,QACnBgD,EApaQ,UAoaG/xB,EACfzB,KAAK0D,YAAY5L,MAAM64B,WACvB3wB,KAAK0D,YAAY5L,MAAM24B,SAEzBnwB,EAAaQ,GAAGd,KAAK2D,SAAU4vB,EAASvzB,KAAK+J,QAAQ9S,SAAUiI,GAASc,KAAKyxB,OAAOvyB,IACpFoB,EAAaQ,GAAGd,KAAK2D,SAAU6vB,EAAUxzB,KAAK+J,QAAQ9S,SAAUiI,GAASc,KAAK0xB,OAAOxyB,OAIzFc,KAAK4xB,kBAAoB,KACnB5xB,KAAK2D,UACP3D,KAAK8O,QAITxO,EAAaQ,GAAGd,KAAK2D,SAASiB,QAvbV,UAEC,gBAqboD5E,KAAK4xB,mBAE1E5xB,KAAK+J,QAAQ9S,SACf+I,KAAK+J,QAAU,IACV/J,KAAK+J,QACRtI,QAAS,SACTxK,SAAU,IAGZ+I,KAAKyzB,YAITA,YACE,MAAM5F,EAAQ7tB,KAAK2D,SAASzM,aAAa,SACnCw8B,SAA2B1zB,KAAK2D,SAASzM,aAAa,2BAExD22B,GAA+B,WAAtB6F,KACX1zB,KAAK2D,SAAS2B,aAAa,yBAA0BuoB,GAAS,KAC1DA,GAAU7tB,KAAK2D,SAASzM,aAAa,eAAkB8I,KAAK2D,SAASuvB,aACvElzB,KAAK2D,SAAS2B,aAAa,aAAcuoB,GAG3C7tB,KAAK2D,SAAS2B,aAAa,QAAS,KAIxCmsB,OAAOvyB,EAAOuoB,GACZA,EAAUznB,KAAKsxB,6BAA6BpyB,EAAOuoB,GAE/CvoB,IACFuoB,EAAQuJ,eACS,YAAf9xB,EAAMsB,KAldQ,QADA,UAodZ,GAGFinB,EAAQkK,gBAAgB53B,UAAUC,SAjelB,SAEC,SA+d8CytB,EAAQsJ,YACzEtJ,EAAQsJ,YAheW,QAoerB9kB,aAAawb,EAAQqJ,UAErBrJ,EAAQsJ,YAtea,OAwehBtJ,EAAQ1d,QAAQ+jB,OAAUrG,EAAQ1d,QAAQ+jB,MAAM/e,KAKrD0Y,EAAQqJ,SAAW5zB,WAAW,KA7eT,SA8efuqB,EAAQsJ,aACVtJ,EAAQ1Y,QAET0Y,EAAQ1d,QAAQ+jB,MAAM/e,MARvB0Y,EAAQ1Y,QAWZ2iB,OAAOxyB,EAAOuoB,GACZA,EAAUznB,KAAKsxB,6BAA6BpyB,EAAOuoB,GAE/CvoB,IACFuoB,EAAQuJ,eACS,aAAf9xB,EAAMsB,KAhfQ,QADA,SAkfZinB,EAAQ9jB,SAAS3J,SAASkF,EAAMY,gBAGlC2nB,EAAQ+J,yBAIZvlB,aAAawb,EAAQqJ,UAErBrJ,EAAQsJ,YAlgBY,MAogBftJ,EAAQ1d,QAAQ+jB,OAAUrG,EAAQ1d,QAAQ+jB,MAAMhf,KAKrD2Y,EAAQqJ,SAAW5zB,WAAW,KAzgBV,QA0gBduqB,EAAQsJ,aACVtJ,EAAQ3Y,QAET2Y,EAAQ1d,QAAQ+jB,MAAMhf,MARvB2Y,EAAQ3Y,QAWZ0iB,uBACE,IAAK,MAAM/vB,KAAWzB,KAAKgxB,eACzB,GAAIhxB,KAAKgxB,eAAevvB,GACtB,OAAO,EAIX,OAAO,EAGTuI,WAAWzR,GACT,MAAMo7B,EAAiB/tB,EAAYI,kBAAkBhG,KAAK2D,UAqC1D,OAnCAlL,OAAOC,KAAKi7B,GAAgBh7B,QAAQi7B,IAC9BlG,GAAsBhuB,IAAIk0B,WACrBD,EAAeC,MAI1Br7B,EAAS,IACJyH,KAAK0D,YAAY4E,WACjBqrB,KACmB,iBAAXp7B,GAAuBA,EAASA,EAAS,KAG/C2W,WAAiC,IAArB3W,EAAO2W,UAAsBzX,SAASuD,KAAO7C,EAAWI,EAAO2W,WAEtD,iBAAjB3W,EAAOu1B,QAChBv1B,EAAOu1B,MAAQ,CACb/e,KAAMxW,EAAOu1B,MACbhf,KAAMvW,EAAOu1B,QAIW,iBAAjBv1B,EAAOs1B,QAChBt1B,EAAOs1B,MAAQt1B,EAAOs1B,MAAM70B,YAGA,iBAAnBT,EAAOw6B,UAChBx6B,EAAOw6B,QAAUx6B,EAAOw6B,QAAQ/5B,YAGlCX,EAroBS,UAqoBaE,EAAQyH,KAAK0D,YAAYmF,aAE3CtQ,EAAOy1B,WACTz1B,EAAOq1B,SAAWd,GAAav0B,EAAOq1B,SAAUr1B,EAAOy0B,UAAWz0B,EAAO00B,aAGpE10B,EAGT66B,qBACE,MAAM76B,EAAS,GAEf,IAAK,MAAMiK,KAAOxC,KAAK+J,QACjB/J,KAAK0D,YAAY4E,QAAQ9F,KAASxC,KAAK+J,QAAQvH,KACjDjK,EAAOiK,GAAOxC,KAAK+J,QAAQvH,IAO/B,OAAOjK,EAGTo6B,iBACE,MAAM1B,EAAMjxB,KAAK2xB,gBACXkC,EAAwB,IAAIz6B,OAAQ,UAAS4G,KAAKszB,6BAA8B,KAChFQ,EAAW7C,EAAI/5B,aAAa,SAASgC,MAAM26B,GAChC,OAAbC,GAAqBA,EAAS17B,OAAS,GACzC07B,EAAS3rB,IAAI4rB,GAASA,EAAMx8B,QACzBoB,QAAQq7B,GAAU/C,EAAIl3B,UAAUwJ,OAAOywB,IAI9CV,uBACE,MArqBiB,aAwqBnBD,6BAA6BjM,GAC3B,MAAMjW,MAAEA,GAAUiW,EAEbjW,IAILnR,KAAKixB,IAAM9f,EAAMC,SAASM,OAC1B1R,KAAK2yB,iBACL3yB,KAAKwyB,oBAAoBxyB,KAAKuyB,eAAephB,EAAMhB,aAK/BhM,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO0rB,GAAQ/rB,oBAAoB7E,KAAMzH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAab6C,EAAmBw1B,IC/tBnB,MAKMtoB,GAAU,IACXsoB,GAAQtoB,QACX6H,UAAW,QACX5J,OAAQ,CAAC,EAAG,GACZ9E,QAAS,QACTsxB,QAAS,GACTnF,SAAU,+IAON/kB,GAAc,IACf+nB,GAAQ/nB,YACXkqB,QAAS,6BAGLj7B,GAAQ,CACZo4B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAYf,MAAMsD,WAAgBrD,GAGFtoB,qBAChB,OAAOA,GAGM7M,kBACb,MArDS,UAwDK3D,mBACd,OAAOA,GAGa+Q,yBACpB,OAAOA,GAKTgpB,gBACE,OAAO7xB,KAAK4yB,YAAc5yB,KAAKk0B,cAGjCrB,WAAW5B,GACTjxB,KAAK8yB,uBAAuB7B,EAAKjxB,KAAK4yB,WAnCnB,mBAoCnB5yB,KAAK8yB,uBAAuB7B,EAAKjxB,KAAKk0B,cAnCjB,iBAwCvBA,cACE,OAAOl0B,KAAKyyB,yBAAyBzyB,KAAK+J,QAAQgpB,SAGpDO,uBACE,MA/EiB,aAoFGnvB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO+uB,GAAQpvB,oBAAoB7E,KAAMzH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAab6C,EAAmB64B,ICrGnB,MAKM3rB,GAAU,CACd/B,OAAQ,GACR9B,OAAQ,OACRzH,OAAQ,IAGJ6L,GAAc,CAClBtC,OAAQ,SACR9B,OAAQ,SACRzH,OAAQ,oBAeJm3B,GAAuB,8CAa7B,MAAMC,WAAkB3wB,EACtBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GACNgJ,KAAKq0B,eAA2C,SAA1Br0B,KAAK2D,SAASgB,QAAqB5J,OAASiF,KAAK2D,SACvE3D,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKs0B,SAAW,GAChBt0B,KAAKu0B,SAAW,GAChBv0B,KAAKw0B,cAAgB,KACrBx0B,KAAKy0B,cAAgB,EAErBn0B,EAAaQ,GAAGd,KAAKq0B,eAlCH,sBAkCiC,IAAMr0B,KAAK00B,YAE9D10B,KAAK20B,UACL30B,KAAK00B,WAKWpsB,qBAChB,OAAOA,GAGM7M,kBACb,MAjES,YAsEXk5B,UACE,MAAMC,EAAa50B,KAAKq0B,iBAAmBr0B,KAAKq0B,eAAet5B,OAtC7C,SACE,WAyCd85B,EAAuC,SAAxB70B,KAAK+J,QAAQtF,OAChCmwB,EACA50B,KAAK+J,QAAQtF,OAETqwB,EA7Cc,aA6CDD,EACjB70B,KAAK+0B,gBACL,EAEF/0B,KAAKs0B,SAAW,GAChBt0B,KAAKu0B,SAAW,GAChBv0B,KAAKy0B,cAAgBz0B,KAAKg1B,mBAEV/tB,EAAeC,KAAKitB,GAAqBn0B,KAAK+J,QAAQ/M,QAE9DmL,IAAInR,IACV,MAAMi+B,EAAiBz9B,EAAuBR,GACxCgG,EAASi4B,EAAiBhuB,EAAeK,QAAQ2tB,GAAkB,KAEzE,GAAIj4B,EAAQ,CACV,MAAMk4B,EAAYl4B,EAAOyJ,wBACzB,GAAIyuB,EAAU1iB,OAAS0iB,EAAUxiB,OAC/B,MAAO,CACL9M,EAAYivB,GAAc73B,GAAQ0J,IAAMouB,EACxCG,GAKN,OAAO,OAEN9uB,OAAOgvB,GAAQA,GACfhY,KAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,IACxB1kB,QAAQw8B,IACPn1B,KAAKs0B,SAASr4B,KAAKk5B,EAAK,IACxBn1B,KAAKu0B,SAASt4B,KAAKk5B,EAAK,MAI9BtxB,UACEvD,EAAaC,IAAIP,KAAKq0B,eAhHP,iBAiHf/qB,MAAMzF,UAKRmG,WAAWzR,GAWT,OAVAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,GAAuBA,EAASA,EAAS,KAG/CyE,OAAS7E,EAAWI,EAAOyE,SAAWvF,SAAS2C,gBAEtD/B,EAjIS,YAiIaE,EAAQsQ,IAEvBtQ,EAGTw8B,gBACE,OAAO/0B,KAAKq0B,iBAAmBt5B,OAC7BiF,KAAKq0B,eAAe1tB,YACpB3G,KAAKq0B,eAAetb,UAGxBic,mBACE,OAAOh1B,KAAKq0B,eAAe/Z,cAAgB3c,KAAKC,IAC9CnG,SAASuD,KAAKsf,aACd7iB,SAAS2C,gBAAgBkgB,cAI7B8a,mBACE,OAAOp1B,KAAKq0B,iBAAmBt5B,OAC7BA,OAAOs6B,YACPr1B,KAAKq0B,eAAe5tB,wBAAwBiM,OAGhDgiB,WACE,MAAM3b,EAAY/Y,KAAK+0B,gBAAkB/0B,KAAK+J,QAAQxD,OAChD+T,EAAeta,KAAKg1B,mBACpBM,EAAYt1B,KAAK+J,QAAQxD,OAAS+T,EAAeta,KAAKo1B,mBAM5D,GAJIp1B,KAAKy0B,gBAAkBna,GACzBta,KAAK20B,UAGH5b,GAAauc,EAAjB,CACE,MAAMt4B,EAASgD,KAAKu0B,SAASv0B,KAAKu0B,SAASn8B,OAAS,GAEhD4H,KAAKw0B,gBAAkBx3B,GACzBgD,KAAKu1B,UAAUv4B,OAJnB,CAUA,GAAIgD,KAAKw0B,eAAiBzb,EAAY/Y,KAAKs0B,SAAS,IAAMt0B,KAAKs0B,SAAS,GAAK,EAG3E,OAFAt0B,KAAKw0B,cAAgB,UACrBx0B,KAAKw1B,SAIP,IAAK,IAAIx2B,EAAIgB,KAAKs0B,SAASl8B,OAAQ4G,KACVgB,KAAKw0B,gBAAkBx0B,KAAKu0B,SAASv1B,IACxD+Z,GAAa/Y,KAAKs0B,SAASt1B,UACM,IAAzBgB,KAAKs0B,SAASt1B,EAAI,IAAsB+Z,EAAY/Y,KAAKs0B,SAASt1B,EAAI,KAGhFgB,KAAKu1B,UAAUv1B,KAAKu0B,SAASv1B,KAKnCu2B,UAAUv4B,GACRgD,KAAKw0B,cAAgBx3B,EAErBgD,KAAKw1B,SAEL,MAAMC,EAAUtB,GAAoB78B,MAAM,KACvC6Q,IAAIlR,GAAa,GAAEA,qBAA4B+F,OAAY/F,WAAkB+F,OAE1E04B,EAAOzuB,EAAeK,QAAQmuB,EAAQrtB,KAAK,KAAMpI,KAAK+J,QAAQ/M,QAEpE04B,EAAK37B,UAAUqS,IAjLO,UAkLlBspB,EAAK37B,UAAUC,SAnLU,iBAoL3BiN,EAAeK,QA1KY,mBA0KsBouB,EAAK9wB,QA3KlC,cA4KjB7K,UAAUqS,IApLO,UAsLpBnF,EAAeS,QAAQguB,EAnLG,qBAoLvB/8B,QAAQg9B,IAGP1uB,EAAeW,KAAK+tB,EAAY,+BAC7Bh9B,QAAQw8B,GAAQA,EAAKp7B,UAAUqS,IA3LlB,WA8LhBnF,EAAeW,KAAK+tB,EAzLH,aA0Ldh9B,QAAQi9B,IACP3uB,EAAeM,SAASquB,EA5LX,aA6LVj9B,QAAQw8B,GAAQA,EAAKp7B,UAAUqS,IAjMtB,eAsMtB9L,EAAamB,QAAQzB,KAAKq0B,eA3MN,wBA2MsC,CACxDv0B,cAAe9C,IAInBw4B,SACEvuB,EAAeC,KAAKitB,GAAqBn0B,KAAK+J,QAAQ/M,QACnDmJ,OAAOsK,GAAQA,EAAK1W,UAAUC,SA7MX,WA8MnBrB,QAAQ8X,GAAQA,EAAK1W,UAAUwJ,OA9MZ,WAmNFY,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOkvB,GAAUvvB,oBAAoB7E,KAAMzH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAWX+H,EAAaQ,GAAG/F,OA7Oa,6BA6OgB,KAC3CkM,EAAeC,KAzOS,0BA0OrBvO,QAAQk9B,GAAO,IAAIzB,GAAUyB,MAUlCz6B,EAAmBg5B,IC/OnB,MAAM0B,WAAYryB,EAGDhI,kBACb,MAlCS,MAuCXsT,OACE,GAAK/O,KAAK2D,SAASlJ,YACjBuF,KAAK2D,SAASlJ,WAAWvC,WAAa2B,KAAKC,cAC3CkG,KAAK2D,SAAS5J,UAAUC,SA9BJ,UA+BpB,OAGF,IAAI6N,EACJ,MAAM7K,EAASrF,EAAuBqI,KAAK2D,UACrCoyB,EAAc/1B,KAAK2D,SAASiB,QA/BN,qBAiC5B,GAAImxB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAYxlB,UAA8C,OAAzBwlB,EAAYxlB,SAhC7C,wBADH,UAkClB1I,EAAWZ,EAAeC,KAAK8uB,EAAcD,GAC7CluB,EAAWA,EAASA,EAASzP,OAAS,GAGxC,MAAM69B,EAAYpuB,EAChBvH,EAAamB,QAAQoG,EApDP,cAoD6B,CACzC/H,cAAeE,KAAK2D,WAEtB,KAMF,GAJkBrD,EAAamB,QAAQzB,KAAK2D,SAvD5B,cAuDkD,CAChE7D,cAAe+H,IAGH9F,kBAAmC,OAAdk0B,GAAsBA,EAAUl0B,iBACjE,OAGF/B,KAAKu1B,UAAUv1B,KAAK2D,SAAUoyB,GAE9B,MAAMG,EAAW,KACf51B,EAAamB,QAAQoG,EAnEL,gBAmE6B,CAC3C/H,cAAeE,KAAK2D,WAEtBrD,EAAamB,QAAQzB,KAAK2D,SApEX,eAoEkC,CAC/C7D,cAAe+H,KAIf7K,EACFgD,KAAKu1B,UAAUv4B,EAAQA,EAAOvC,WAAYy7B,GAE1CA,IAMJX,UAAUv+B,EAASkY,EAAW5T,GAC5B,MAIM66B,IAJiBjnB,GAAqC,OAAvBA,EAAUqB,UAA4C,OAAvBrB,EAAUqB,SAE5EtJ,EAAeM,SAAS2H,EA3EN,WA0ElBjI,EAAeC,KAzEM,wBAyEmBgI,IAGZ,GACxBknB,EAAkB96B,GAAa66B,GAAUA,EAAOp8B,UAAUC,SAnF5C,QAqFdk8B,EAAW,IAAMl2B,KAAKq2B,oBAAoBr/B,EAASm/B,EAAQ76B,GAE7D66B,GAAUC,GACZD,EAAOp8B,UAAUwJ,OAvFC,QAwFlBvD,KAAKiE,eAAeiyB,EAAUl/B,GAAS,IAEvCk/B,IAIJG,oBAAoBr/B,EAASm/B,EAAQ76B,GACnC,GAAI66B,EAAQ,CACVA,EAAOp8B,UAAUwJ,OAlGG,UAoGpB,MAAM+yB,EAAgBrvB,EAAeK,QA1FJ,kCA0F4C6uB,EAAO17B,YAEhF67B,GACFA,EAAcv8B,UAAUwJ,OAvGN,UA0GgB,QAAhC4yB,EAAOj/B,aAAa,SACtBi/B,EAAO7wB,aAAa,iBAAiB,GAIzCtO,EAAQ+C,UAAUqS,IA/GI,UAgHe,QAAjCpV,EAAQE,aAAa,SACvBF,EAAQsO,aAAa,iBAAiB,GAGxC3K,EAAO3D,GAEHA,EAAQ+C,UAAUC,SArHF,SAsHlBhD,EAAQ+C,UAAUqS,IArHA,QAwHpB,IAAI8B,EAASlX,EAAQyD,WAKrB,GAJIyT,GAA8B,OAApBA,EAAOqC,WACnBrC,EAASA,EAAOzT,YAGdyT,GAAUA,EAAOnU,UAAUC,SAhIF,iBAgIsC,CACjE,MAAMu8B,EAAkBv/B,EAAQ4N,QA5HZ,aA8HhB2xB,GACFtvB,EAAeC,KA1HU,mBA0HqBqvB,GAC3C59B,QAAQ69B,GAAYA,EAASz8B,UAAUqS,IApIxB,WAuIpBpV,EAAQsO,aAAa,iBAAiB,GAGpChK,GACFA,IAMkB6I,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO4wB,GAAIjxB,oBAAoB7E,MAErC,GAAsB,iBAAXzH,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAYb+H,EAAaQ,GAAGrJ,SAzKc,wBAWD,4EA8JyC,SAAUyH,GAC1E,CAAC,IAAK,QAAQ9H,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,OAIF81B,GAAIjxB,oBAAoB7E,MAChC+O,UAUP3T,EAAmB06B,ICtMnB,MAkBMjtB,GAAc,CAClB8kB,UAAW,UACX8I,SAAU,UACV3I,MAAO,UAGHxlB,GAAU,CACdqlB,WAAW,EACX8I,UAAU,EACV3I,MAAO,KAST,MAAM4I,WAAcjzB,EAClBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAK8wB,SAAW,KAChB9wB,KAAK22B,sBAAuB,EAC5B32B,KAAK42B,yBAA0B,EAC/B52B,KAAKkxB,gBAKeroB,yBACpB,OAAOA,GAGSP,qBAChB,OAAOA,GAGM7M,kBACb,MA1DS,QA+DXsT,OACoBzO,EAAamB,QAAQzB,KAAK2D,SAtD5B,iBAwDF5B,mBAId/B,KAAK62B,gBAED72B,KAAK+J,QAAQ4jB,WACf3tB,KAAK2D,SAAS5J,UAAUqS,IA5DN,QAsEpBpM,KAAK2D,SAAS5J,UAAUwJ,OArEJ,QAsEpB5I,EAAOqF,KAAK2D,UACZ3D,KAAK2D,SAAS5J,UAAUqS,IAtEJ,QAuEpBpM,KAAK2D,SAAS5J,UAAUqS,IAtED,WAwEvBpM,KAAKiE,eAZY,KACfjE,KAAK2D,SAAS5J,UAAUwJ,OA7DH,WA8DrBjD,EAAamB,QAAQzB,KAAK2D,SAnEX,kBAqEf3D,KAAK82B,sBAQuB92B,KAAK2D,SAAU3D,KAAK+J,QAAQ4jB,YAG5D7e,OACO9O,KAAK2D,SAAS5J,UAAUC,SA7ET,UAiFFsG,EAAamB,QAAQzB,KAAK2D,SAxF5B,iBA0FF5B,mBAWd/B,KAAK2D,SAAS5J,UAAUqS,IA7FD,WA8FvBpM,KAAKiE,eARY,KACfjE,KAAK2D,SAAS5J,UAAUqS,IAzFN,QA0FlBpM,KAAK2D,SAAS5J,UAAUwJ,OAxFH,WAyFrBvD,KAAK2D,SAAS5J,UAAUwJ,OA1FN,QA2FlBjD,EAAamB,QAAQzB,KAAK2D,SAjGV,oBAqGY3D,KAAK2D,SAAU3D,KAAK+J,QAAQ4jB,aAG5D9pB,UACE7D,KAAK62B,gBAED72B,KAAK2D,SAAS5J,UAAUC,SArGR,SAsGlBgG,KAAK2D,SAAS5J,UAAUwJ,OAtGN,QAyGpB+F,MAAMzF,UAKRmG,WAAWzR,GAST,OARAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,GAAuBA,EAASA,EAAS,IAGtDF,EApIS,QAoIaE,EAAQyH,KAAK0D,YAAYmF,aAExCtQ,EAGTu+B,qBACO92B,KAAK+J,QAAQ0sB,WAIdz2B,KAAK22B,sBAAwB32B,KAAK42B,0BAItC52B,KAAK8wB,SAAW5zB,WAAW,KACzB8C,KAAK8O,QACJ9O,KAAK+J,QAAQ+jB,SAGlBiJ,eAAe73B,EAAO83B,GACpB,OAAQ93B,EAAMsB,MACZ,IAAK,YACL,IAAK,WACHR,KAAK22B,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACHh3B,KAAK42B,wBAA0BI,EAMnC,GAAIA,EAEF,YADAh3B,KAAK62B,gBAIP,MAAMzpB,EAAclO,EAAMY,cACtBE,KAAK2D,WAAayJ,GAAepN,KAAK2D,SAAS3J,SAASoT,IAI5DpN,KAAK82B,qBAGP5F,gBACE5wB,EAAaQ,GAAGd,KAAK2D,SA/KA,qBA+K2BzE,GAASc,KAAK+2B,eAAe73B,GAAO,IACpFoB,EAAaQ,GAAGd,KAAK2D,SA/KD,oBA+K2BzE,GAASc,KAAK+2B,eAAe73B,GAAO,IACnFoB,EAAaQ,GAAGd,KAAK2D,SA/KF,mBA+K2BzE,GAASc,KAAK+2B,eAAe73B,GAAO,IAClFoB,EAAaQ,GAAGd,KAAK2D,SA/KD,oBA+K2BzE,GAASc,KAAK+2B,eAAe73B,GAAO,IAGrF23B,gBACE5qB,aAAajM,KAAK8wB,UAClB9wB,KAAK8wB,SAAW,KAKI3sB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOwxB,GAAM7xB,oBAAoB7E,KAAMzH,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,kBAMrBuE,EAAqBmyB,IASrBt7B,EAAmBs7B,IC3NJ,CACb5xB,MAAAA,EACAM,OAAAA,EACAiE,SAAAA,EACA8E,SAAAA,GACAgY,SAAAA,GACAmE,MAAAA,GACA0B,UAAAA,GACAiI,QAAAA,GACAG,UAAAA,GACA0B,IAAAA,GACAY,MAAAA,GACA9F,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  executeAfterTransition,\n  getElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.1.0'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nenableDismissTrigger(Alert, 'close')\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + window.pageYOffset,\n      left: rect.left + window.pageXOffset\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(', ')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  getNextActiveElement,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(direction)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    return getNextActiveElement(this._items, activeElement, isNext, this._config.wrap)\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    if (this._isSliding) {\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    const data = Carousel.getOrCreateInstance(element, config)\n\n    let { _config } = data\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Carousel.getInstance(target).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Carousel.getInstance(carousels[i]))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: null\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(null|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let actives = []\n    let activesData\n\n    if (this._config.parent) {\n      const children = SelectorEngine.find(`.${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`, this._config.parent)\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._config.parent).filter(elem => !children.includes(elem)) // remove children if greater depth\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives.length) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Collapse.getInstance(tempActiveData) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    actives.forEach(elemActive => {\n      if (container !== elemActive) {\n        Collapse.getOrCreateInstance(elemActive, { toggle: false }).hide()\n      }\n\n      if (!activesData) {\n        Data.set(elemActive, DATA_KEY, null)\n      }\n    })\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    for (let i = 0; i < triggerArrayLength; i++) {\n      const trigger = this._triggerArray[i]\n      const elem = getElementFromSelector(trigger)\n\n      if (elem && !this._isShown(elem)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = SelectorEngine.find(`.${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`, this._config.parent)\n    SelectorEngine.find(SELECTOR_DATA_TOGGLE, this._config.parent).filter(elem => !children.includes(elem))\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        if (selected) {\n          this._addAriaAndCollapsedClass([element], this._isShown(selected))\n        }\n      })\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const _config = {}\n      if (typeof config === 'string' && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "import { isHTMLElement } from \"./instanceOf.js\";\nvar round = Math.round;\nexport default function getBoundingClientRect(element, includeScale) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  var rect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (isHTMLElement(element) && includeScale) {\n    // Fallback to 1 in case both values are `0`\n    scaleX = rect.width / element.offsetWidth || 1;\n    scaleY = rect.height / element.offsetHeight || 1;\n  }\n\n  return {\n    width: round(rect.width / scaleX),\n    height: round(rect.height / scaleY),\n    top: round(rect.top / scaleY),\n    right: round(rect.right / scaleX),\n    bottom: round(rect.bottom / scaleY),\n    left: round(rect.left / scaleX),\n    x: round(rect.left / scaleX),\n    y: round(rect.top / scaleY)\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var isIE = navigator.userAgent.indexOf('Trident') !== -1;\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport default function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "import { top, left, right, bottom } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top) {\n      sideY = bottom; // $FlowFixMe[prop-missing]\n\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left) {\n      sideX = right; // $FlowFixMe[prop-missing]\n\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) < 2 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var referenceElement = state.elements.reference;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(referenceElement);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { max as mathMax, min as mathMin } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      var _mainSide = mainAxis === 'x' ? top : left;\n\n      var _altSide = mainAxis === 'x' ? bottom : right;\n\n      var _offset = popperOffsets[altAxis];\n\n      var _min = _offset + overflow[_mainSide];\n\n      var _max = _offset - overflow[_altSide];\n\n      var _preventedOffset = within(tether ? mathMin(_min, tetherMin) : _min, _offset, tether ? mathMax(_max, tetherMax) : _max);\n\n      popperOffsets[altAxis] = _preventedOffset;\n      data[altAxis] = _preventedOffset - _offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = rect.width / element.offsetWidth || 1;\n  var scaleY = rect.height / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(options) {\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      this._createPopper(parent)\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper(parent) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n    if (isDisplayStatic) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n    }\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Dropdown.getInstance(toggles[i])\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._isShown()) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (event.key === ESCAPE_KEY) {\n      instance.hide()\n      return\n    }\n\n    if (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY) {\n      if (!isActive) {\n        instance.show()\n      }\n\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.append(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport { typeCheckConfig } from './index'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nclass FocusTrap {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  activate() {\n    const { trapElement, autofocus } = this._config\n\n    if (this._isActive) {\n      return\n    }\n\n    if (autofocus) {\n      trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n\n  _handleFocusin(event) {\n    const { target } = event\n    const { trapElement } = this._config\n\n    if (\n      target === document ||\n      target === trapElement ||\n      trapElement.contains(target)\n    ) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const { classList, scrollHeight, style } = this._element\n    const isModalOverflowing = scrollHeight > document.documentElement.clientHeight\n\n    // return if the following background transition hasn't yet completed\n    if ((!isModalOverflowing && style.overflowY === 'hidden') || classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      style.overflowY = 'hidden'\n    }\n\n    classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        this._queueCallback(() => {\n          style.overflowY = ''\n        }, this._dialog)\n      }\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll) {\n        this._focustrap.activate()\n      }\n\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () =>\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => Offcanvas.getOrCreateInstance(el).show())\n)\n\nenableDismissTrigger(Offcanvas)\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport { DefaultAllowlist, sanitizeHtml } from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = this._resolvePossibleFunction(this._config.customClass)\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW) {\n        tip.remove()\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    const tip = element.children[0]\n    this.setContent(tip)\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n\n    this.tip = tip\n    return this.tip\n  }\n\n  setContent(tip) {\n    this._sanitizeAndSetContent(tip, this.getTitle(), SELECTOR_TOOLTIP_INNER)\n  }\n\n  _sanitizeAndSetContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!content && templateElement) {\n      templateElement.remove()\n      return\n    }\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(templateElement, content)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.append(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    const title = this._element.getAttribute('data-bs-original-title') || this._config.title\n\n    return this._resolvePossibleFunction(title)\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    return context || this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(content) {\n    return typeof content === 'function' ? content.call(this._element) : content\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${this._getBasicClassPrefix()}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const basicClassPrefixRegex = new RegExp(`(^|\\\\s)${this._getBasicClassPrefix()}\\\\S+`, 'g')\n    const tabClass = tip.getAttribute('class').match(basicClassPrefixRegex)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _getBasicClassPrefix() {\n    return CLASS_PREFIX\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent(tip) {\n    this._sanitizeAndSetContent(tip, this.getTitle(), SELECTOR_TITLE)\n    this._sanitizeAndSetContent(tip, this._getContent(), SELECTOR_CONTENT)\n  }\n\n  // Private\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  _getBasicClassPrefix() {\n    return CLASS_PREFIX\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}, .${CLASS_NAME_DROPDOWN_ITEM}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(SELECTOR_LINK_ITEMS, this._config.target)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.target = getElement(config.target) || document.documentElement\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = SELECTOR_LINK_ITEMS.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','), this._config.target)\n\n    link.classList.add(CLASS_NAME_ACTIVE)\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(SELECTOR_LINK_ITEMS, this._config.target)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Tab.getOrCreateInstance(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\nenableDismissTrigger(Toast)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.0): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}