{"format": 1, "restore": {"D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\DrugPrevention.MVCWebApp.NamND.csproj": {}}, "projects": {"D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\DrugPrevention.MVCWebApp.NamND.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\DrugPrevention.MVCWebApp.NamND.csproj", "projectName": "DrugPrevention.MVCWebApp.NamND", "projectPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\DrugPrevention.MVCWebApp.NamND.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.MVCWebApp.NamND\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\DrugPrevention.Services.NamND.csproj": {"projectPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\DrugPrevention.Services.NamND.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[8.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Repositories.NamND\\DrugPrevention.Repositories.NamND.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Repositories.NamND\\DrugPrevention.Repositories.NamND.csproj", "projectName": "DrugPrevention.Repositories.NamND", "projectPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Repositories.NamND\\DrugPrevention.Repositories.NamND.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Repositories.NamND\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\DrugPrevention.Services.NamND.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\DrugPrevention.Services.NamND.csproj", "projectName": "DrugPrevention.Services.NamND", "projectPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\DrugPrevention.Services.NamND.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Services.NamND\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Repositories.NamND\\DrugPrevention.Repositories.NamND.csproj": {"projectPath": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\4\\SU25_PRN222_SE1709_ASM4_SE183115_NamND\\DrugPrevention.Repositories.NamND\\DrugPrevention.Repositories.NamND.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}