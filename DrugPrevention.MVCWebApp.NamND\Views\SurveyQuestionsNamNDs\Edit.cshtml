﻿@model DrugPrevention.Repositories.NamND.Models.SurveyQuestionsNamND

@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>SurveyQuestionsNamND</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="QuestionNamNDID" />
            <div class="form-group">
                <label asp-for="SurveyNamNDID" class="control-label"></label>
                <select asp-for="SurveyNamNDID" class="form-control" asp-items="ViewBag.SurveyNamNDID"></select>
                <span asp-validation-for="SurveyNamNDID" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="QuestionText" class="control-label"></label>
                <input asp-for="QuestionText" class="form-control" />
                <span asp-validation-for="QuestionText" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="QuestionOrder" class="control-label"></label>
                <input asp-for="QuestionOrder" class="form-control" />
                <span asp-validation-for="QuestionOrder" class="text-danger"></span>
            </div>
            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="IsActive" /> @Html.DisplayNameFor(model => model.IsActive)
                </label>
            </div>
            <div class="form-group">
                <label asp-for="CreatedDate" class="control-label"></label>
                <input asp-for="CreatedDate" class="form-control" />
                <span asp-validation-for="CreatedDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="QuestionType" class="control-label"></label>
                <input asp-for="QuestionType" class="form-control" />
                <span asp-validation-for="QuestionType" class="text-danger"></span>
            </div>
            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="IsRequired" /> @Html.DisplayNameFor(model => model.IsRequired)
                </label>
            </div>
            <div class="form-group">
                <label asp-for="Options" class="control-label"></label>
                <input asp-for="Options" class="form-control" />
                <span asp-validation-for="Options" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="RiskWeight" class="control-label"></label>
                <input asp-for="RiskWeight" class="form-control" />
                <span asp-validation-for="RiskWeight" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="HelpText" class="control-label"></label>
                <input asp-for="HelpText" class="form-control" />
                <span asp-validation-for="HelpText" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Section" class="control-label"></label>
                <input asp-for="Section" class="form-control" />
                <span asp-validation-for="Section" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Tag" class="control-label"></label>
                <input asp-for="Tag" class="form-control" />
                <span asp-validation-for="Tag" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Language" class="control-label"></label>
                <input asp-for="Language" class="form-control" />
                <span asp-validation-for="Language" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DependsOnQuestionID" class="control-label"></label>
                <input asp-for="DependsOnQuestionID" class="form-control" />
                <span asp-validation-for="DependsOnQuestionID" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DependsOnAnswer" class="control-label"></label>
                <input asp-for="DependsOnAnswer" class="form-control" />
                <span asp-validation-for="DependsOnAnswer" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="MinScore" class="control-label"></label>
                <input asp-for="MinScore" class="form-control" />
                <span asp-validation-for="MinScore" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="MaxScore" class="control-label"></label>
                <input asp-for="MaxScore" class="form-control" />
                <span asp-validation-for="MaxScore" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DisplayStyle" class="control-label"></label>
                <input asp-for="DisplayStyle" class="form-control" />
                <span asp-validation-for="DisplayStyle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ImageURL" class="control-label"></label>
                <input asp-for="ImageURL" class="form-control" />
                <span asp-validation-for="ImageURL" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
